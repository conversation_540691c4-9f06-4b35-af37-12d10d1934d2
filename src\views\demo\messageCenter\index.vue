<template>
  <div class="message-center-demo">
    <div class="demo-header">
      <h1>消息中心演示页面</h1>
      <p>这是一个独立的消息中心演示页面，展示完整的消息管理功能</p>
    </div>
    
    <div class="demo-content">
      <MessageCenter />
    </div>
  </div>
</template>

<script setup lang="ts">
import MessageCenter from '/@/views/userAccessControl/components/MessageCenter.vue';
</script>

<style scoped lang="less">
.message-center-demo {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    font-size: 32px;
    font-weight: bold;
    color: #000;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
