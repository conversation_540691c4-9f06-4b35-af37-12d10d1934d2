import { createGroup } from "dingtalk-jsapi/plugin/coolAppSdk";
import { create } from "sortablejs";

export default {
  group: {
    groupName: 'GroupName',
    groupSpace: 'WorkSpace',
    remark: 'Remark',
    title: 'Group',
    device: "Device",
    deviceType: "Device Type",
    deviceModel: "Device Model",
    deviceBrand: "Device Brand",
    deviceGroup: "Device Group",
    workspace_name: "Workspace Name",
    workspace_location: "Workspace Location",
    groupSpaceSelect: "Please Select Group Space",
    detail: 'Group Detail',
    edit: 'Edit Group',
    add: 'Add Group',
    iconUrl: 'Picture Information',
    username: 'username',
    realname: 'realname',
    sex: 'sex',
    email: 'email',
    phone: 'phone',
    birthday: 'birthday',
    status: 'status',
    groupDeviceSelect: 'Please Select Device',
  },
  userinfo: {
    title: 'User Information',
    detail: 'User Details',
    edit: 'Edit User',
    add: 'Add User',
    username: 'System Account',
    realname: 'User Name',
    sex: 'Gender',
    email: 'Email',
    phone: 'Phone Number',
    birthday: 'Birthday',
    status: 'User Status',
    userType: 'User Type',
    addOldUser: 'Add Existing User',
    userSource: 'User Source',
    remark: 'Remark',

    approveRefuse: 'Approve Refusal',
    approvePass: 'Approve Pass',
    unApprove: 'Withdraw Approval',
    approve: 'Approve',
    ListExists: 'User Information Already Exists!',
    approveRefuseSuccess: 'Approve Refusal Successful',
    approvePassSuccess: 'Approve Pass Successful',
    unApproveSuccess: 'Withdraw Approval Successful',

    approveRefuseError: 'Approve Refusal Failed',
    approvePassError: 'Approve Pass Failed',
    unApproveError: 'Withdraw Approval Failed',

    approveSelect: 'Please Select Data to Approve',
  },

  workspace: {
    title: 'Workspace',
    detail: 'Workspace Detail',
    edit: 'Edit Workspace',
    add: 'Add Workspace',
    workspaceName: 'Name',
    workspaceLocation: 'Location',

    locationDetail: 'Device Location Details',
    locationEdit: 'Edit Device Location',
    locationAdd: 'Add Device Location',

    memberDetail: 'Space Member Details',
    memberEdit: 'Edit Space Member',
    memberAdd: 'Add Space Member',

    inviteDetail: 'Invite Record Details',
    inviteEdit: 'Edit Invite Record',
    inviteAdd: 'Add Invite Record',


    remark: 'Remark',
    deviceLocation: 'Device Location',
    workspaceMembers: 'Workspace Members',
    inviteRecords: 'Invite Records',
    editSuccess: 'Edit Workspace Success',
    deleteSuccess: 'Delete Workspace Success',
    deleteError: 'Delete Workspace Failed',
    deleteSelect: 'Please select the data to delete',
    longitude: 'Longitude',
    latitude: 'Latitude',
    memberRole: 'Member Role',
    inviteTime: 'Invite Time',
    joinTime: 'Join Time',
    beInvited: 'Invited Person',
    inviteType: 'Invite Type',
    invitePhone: 'Phone Number',
    inviteEmail: 'Email',
    memberAccount: 'Link Accounts',
    memberPic: 'Members Avatars',
    confirmDelete: 'Do you want to confirm the deletion? After deletion, all members will be deleted, data will be erased, and added devices will be unbound and reset'
  },
  common: {
    sort: 'Sort',
    all: 'All',
    online: 'Online',
    offline: 'Offline',
    yes: 'Yes',
    no: 'No'
  },
  device: {
    deviceModel: 'Device Management',
    deviceModelAdd: 'Add Device',
    searchPlaceholder: 'Search devices',
    deviceCustomize: 'Customize',

    outdoor: 'Outdoor',
    indoor: 'Indoor',

    manual: 'Manual Mode',
    auto: 'Auto Mode', 
    sleep: 'Sleep Mode',
    environment: 'Environment',
    connection: 'Connection',
    data_publication: 'Data Publication',
    mode: 'Mode',
    pleaseSelect: 'Please select a device',
    addSuccess: 'Device added successfully',
    filter: {
      environment: 'Environment',
      connection: 'Connection',
      data_publication: 'Data Publication',
      mode: 'Mode',
      outdoor: 'Outdoor',
      indoor: 'Indoor',
      manual: 'Manual',
      auto: 'Auto',
      sleep: 'Sleep'
    }
  },
  groupDevice: {
    deviceModel: 'Group Management',
    createGroup: 'Create Group',
    searchPlaceholder: 'Search Groups',
    pleaseSelectGroup: 'Please select a group',
    pleaseSelectDevice: 'Please select a device to view charts',
  },
    msgCategory:{
    deviceLocation:'deviceLocation',
    notice:'Announcement Notice',
    message:'System Message',
  },
  dataAnalyticsReport: {
    title: 'Data Analytics & Reporting',
    dataOnFilterLife: 'Data on filter life',
    selectDevice: 'Select device',
    exportData: 'Export data',
    filterLifePeriod: 'Filter life period',
    
    // Filter life related
    filterLife: {
      title: 'Data on filter life',
      description: 'Select the device to export based on the upcoming expiration date of the filter.',
      periods: {
        '1': '16%',
        '2': '33%',
        '3': '50%',
        'oneWeek': '1 Week',
        'twoWeek': '2 Week',
        'thirdWeek': '3 Week',
      }
    },
    
    // Air quality related
    airQuality: {
      title: 'Data on air quality',
      description: 'Select pollution levels for filtering',
      pollutionLevel: 'Pollution Level',
      levels: {
        '1': 'Excellent',
        '2': 'Good',
        '3': 'Light Pollution',
        '4': 'Moderate Pollution',
        '5': 'Heavy Pollution',
        '6': 'Severe Pollution'
      },
      selectedCount: 'Selected: {count} pollution levels',
      periods: {
        '1': '1 Day',
        '3': '3 Days',
        '7': '7 Days or More'
      }
    },
    
    // Activity level related
    activityLevel: {
      title: 'Activity Level',
      description: 'Filter users or machines based on weekly activity level.',
      levels: {
        '1': '1 Hour',
        '2': '1 Hours ~ 10 Hours',
        '3': '10 Hours ~ 50 Hours',
        '4': '50 Hours ~ 80 Hours',
        '5': '80 Hours or More'
      }
    },
    
    // Export parameters related
    exportParams: {
      title: 'Export Parameters',
      description: 'Select parameters for this export.',
      dataFormat: 'Data format',
      formats: {
        'filter-life': 'Filter life',
        'air-quality': 'Air quality',
        'activity-level': 'Activity level',
        'all': 'All data'
      }
    },
       // 经销商选择相关
       providerSelection: {
        title: 'Export Scope',
        description: 'Select the scope of data to export',
        allProviders: 'All Providers',
        specificProviders: 'Specific Providers',
        selectProviders: 'Select Providers'
      },
      
      // 经销商选择器
      providerSelector: {
        title: 'Select Providers',
        loading: 'Loading...',
        selectAll: 'Select All',
        selectedCount: '({selected}/{total})',
        searchPlaceholder: 'Search by provider name, country or region...',
        noResults: 'No matching providers found'
      },
    // Action buttons
    actions: {
      clearAll: 'Clear all',
      exportData: 'Export data',
      exporting: 'Exporting...',
      cancel: 'Cancel',
      confirm: 'Confirm'
    },
    
    // Device selector
    deviceSelector: {
      title: 'Select Device Model',
      loading: 'Loading...',
      selectAll: 'Select All',
      selectedCount: '({selected}/{total})'
    },
    
    // Messages
    messages: {
      selectDeviceWarning: 'Please select devices to export',
      selectPollutionLevelWarning: 'Please select pollution levels',
      exportSuccess: 'Data export successful!',
      exportError: 'Data export failed, please try again',
      downloadError: 'File download failed'
    }
  },
  
  // Location Overview Page
  locationOverview: {
    title: 'Location Overview',
    workspace: {
      title: 'Workspace',
      placeholder: 'Please select workspace'
    },
    tabs: {
      outdoor: 'Outdoor',
      indoor: 'Indoor'
    },
    metrics: {
      aqi: 'AQI',
      pm25: 'PM2.5',
      co2: 'CO₂',
      o3: 'O₃',
      temperature: 'Temperature',
      humidity: 'Humidity',
      pressure: 'Pressure'
    },
    map: {
      controls: {
        zoomIn: 'Zoom In',
        zoomOut: 'Zoom Out',
        top: 'Top'
      },
      legend: {
        good: 'Good',
        moderate: 'Moderate',
        unhealthySensitive: 'Unhealthy for Sensitive Groups',
        unhealthy: 'Unhealthy',
        veryUnhealthy: 'Very Unhealthy',
        hazardous: 'Hazardous'
      }
    },
    weather: {
      title: 'Weather',
      localTime: 'Local Time',
      outdoor: 'Outdoor',
      aqi: 'AQI(US)',
      metrics: {
        temp: 'Temp.',
        humidity: 'RH.',
        pm25: 'PM2.5',
        co2: 'CO₂'
      },
      units: {
        celsius: '°C',
        fahrenheit: '°F',
        percent: '%',
        ugm3: 'μg/m³',
        ppm: 'ppm'
      }
    },
    indoor: {
      title: 'Indoor',
      roomSelector: 'Room Selector',
      deviceInfo: {
        device: 'Device',
        totalDevices: 'Total Devices',
        connection: 'Connection'
      },
      status: {
        allOnline: 'All Online',
        allOffline: 'All Offline',
        mixed: '{online} Online, {offline} Offline',
        noDevices: 'No devices'
      },
      airQuality: {
        excellent: 'Excellent',
        veryGood: 'Very Good',
        good: 'Good',
        fair: 'Fair',
        poor: 'Poor',
        veryPoor: 'Very Poor',
        noData: 'No Data'
      }
    },
    airQuality: {
      good: 'Good',
      moderate: 'Moderate',
      unhealthySensitive: 'Unhealthy for Sensitive Groups',
      unhealthy: 'Unhealthy',
      veryUnhealthy: 'Very Unhealthy',
      hazardous: 'Hazardous'
    }
  },
  // Help category related translations
  helpType: {
    title: 'Help Categories',
    typeName: 'Category Name',
    parentNode: 'Parent Node',
    add: 'Add Category',
    edit: 'Edit Category',
    detail: 'Category Details',
    delete: 'Delete Category',
    addSub: 'Add Subcategory',
    confirmDelete: 'Are you sure you want to delete?',
    deleteSuccess: 'Delete successful',
    deleteError: 'Delete failed',
    saveSuccess: 'Save successful',
    saveError: 'Save failed',
    pleaseEnterTypeName: 'Please enter category name!',
    pleaseSelectParentNode: 'Please select parent node',
    categoryName: 'Category Name',
    parentCategory: 'Parent Category',
    noParent: 'No Parent',
    level1: 'Level 1 Category',
    level2: 'Level 2 Category',
    cannotAddSubToLevel2: 'Level 2 categories cannot add subcategories',
  },
  helpInfo: {
    title: 'Help Information',
    helpTitle: 'Title',
    helpType: 'Category',
    helpContent: 'Detailed Content',
    helpfulNum: 'Helpful Count',
    notHelpfulNum: 'Not Helpful Count',
    add: 'Add Help',
    edit: 'Edit Help',
    detail: 'Help Details',
    delete: 'Delete Help',
    confirmDelete: 'Are you sure you want to delete?',
    deleteSuccess: 'Delete successful',
    deleteError: 'Delete failed',
    saveSuccess: 'Save successful',
    saveError: 'Save failed',
    pleaseEnterTitle: 'Please enter title!',
    pleaseSelectType: 'Please enter category!',
    pleaseEnterContent: 'Please enter detailed content!',
  },
  helpFeedback: {
    title: 'Problem Feedback',
    userName: 'Feedback User',
    feedbackCategory: 'Problem Category',
    feedbackContent: 'Problem Description',
    questionTime: 'Problem Occurrence Time',
    userPhone: 'User Phone',
    userEmail: 'User Email',
    questionFrequency: 'Problem Frequency',
    isAgree: 'Agree to Authorization',
    attach: 'Image or Video',
    handleStatus: 'Processing Status',
    handleRecord: 'Processing Record',
    handleUser: 'Processor',
    handleTime: 'Processing Time',
    add: 'Add Feedback',
    edit: 'Edit Feedback',
    detail: 'Feedback Details',
    delete: 'Delete Feedback',
    audit: 'Audit',
    auditSuccess: 'Audit successful',
    auditError: 'Audit failed',
    pleaseEnterHandleRecord: 'Please enter processing record',
    confirmDelete: 'Are you sure you want to delete?',
    deleteSuccess: 'Delete successful',
    deleteError: 'Delete failed',
    saveSuccess: 'Save successful',
    saveError: 'Save failed',
    pleaseEnterUserName: 'Please enter feedback user!',
    pleaseEnterFeedbackContent: 'Please enter problem description!',
    pleaseEnterQuestionTime: 'Please select problem occurrence time!',
    pleaseEnterUserPhone: 'Please enter user phone!',
    pleaseEnterUserEmail: 'Please enter user email!',
    pleaseSelectQuestionFrequency: 'Please select problem frequency!',
    pleaseSelectIsAgree: 'Please select agree to authorization!',
    pleaseSelectHandleStatus: 'Please select processing status!',
    pleaseEnterHandleUser: 'Please enter processor!',
    pleaseEnterHandleTime: 'Please select processing time!',
  },
};
