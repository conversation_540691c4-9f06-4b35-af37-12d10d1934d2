import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '创建日期',
    align:"center",
    sorter: true,
    dataIndex: 'createTime'
   },
   {
    title: '用户账号',
    align:"center",
    sorter: true,
    dataIndex: 'userAccount'
   },
   {
    title: '用户昵称',
    align:"center",
    sorter: true,
    dataIndex: 'userName'
   },
   {
    title: 'UUID',
    align:"center",
    dataIndex: 'deviceUuid'
   },
   {
    title: '激活时间',
    align:"center",
    sorter: true,
    dataIndex: 'activeTime'
   },
   {
    title: '激活ip地址',
    align:"center",
    sorter: true,
    dataIndex: 'ipAddress'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "用户账号",
      field: "userAccount",
      component: 'Input',
      //colProps: {span: 6},
	},
	{
      label: "用户昵称",
      field: "userName",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
	{
      label: "UUID",
      field: 'deviceUuid',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "激活时间",
      field: 'activeTime',
      component: 'RangePicker',
      componentProps: {
         showTime: true,
         valueFormat: 'YYYY-MM-DD HH:mm:ss',
         placeholder: ['开始时间', '结束时间']
       },
      //colProps: {span: 6},
 	},
	{
      label: "激活ip地址",
      field: 'ipAddress',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '用户账号',
    field: 'userAccount',
    component: 'Input',
  },
  {
    label: '用户昵称',
    field: 'userName',
    component: 'Input',
  },
  {
    label: 'UUID',
    field: 'deviceUuid',
    component: 'Input',
  },
  {
    label: '激活时间',
    field: 'activeTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '激活ip地址',
    field: 'ipAddress',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  createTime: {title: '创建日期',order: 0,view: 'datetime', type: 'string',},
  userAccount: {title: '用户账号',order: 1,view: 'text', type: 'string',},
  userName: {title: '用户昵称',order: 2,view: 'text', type: 'string',},
  deviceUuid: {title: 'UUID',order: 3,view: 'text', type: 'string',},
  activeTime: {title: '激活时间',order: 4,view: 'datetime-range', type: 'string',},
  ipAddress: {title: '激活ip地址',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}