<template>
    <div class="air-quality-container">
      <!-- Status Text -->
      <div class="status-text">{{ statusText }}</div>
      
      <!-- AQI Value -->
      <!-- <div class="aqi-value">{{ props.value }}</div> -->
      
      <!-- Quality Bar -->
      <div class="quality-bar-container">
        <div class="quality-bar">
          <div class="quality-gradient"></div>
          <div 
            class="quality-indicator" 
            :style="{ left: indicatorPosition + '%', backgroundColor: indicatorColor }"
          ></div>
        </div>
      </div>
      
      <!-- Description -->
      <div class="description-text">{{ description }}</div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  
  // Props
  interface Props {
    value?: number
    type?: 'indoor' | 'outdoor'
    description?: string
  }
  
  const props = withDefaults(defineProps<Props>(), {
    value: 20,
    type: 'indoor',
    description: 'Indoor Air Quality'
  })
  
  const statusText = computed(() => {
    if (props.type === 'indoor') {
      // 室内空气质量枚举值处理 (1-6)
      switch (props.value) {
        case 6: return 'Excellent'
        case 5: return 'Very Good'
        case 4: return 'Good'
        case 3: return 'Fair'
        case 2: return 'Poor'
        case 1: return 'Very Poor'
        default: return 'No Data'
      }
    } else {
      // 室外AQI值处理 (原有逻辑)
      if (props.value <= 50) return 'Good'
      if (props.value <= 100) return 'Moderate'
      if (props.value <= 150) return 'Unhealthy for Sensitive Groups'
      if (props.value <= 200) return 'Unhealthy'
      if (props.value <= 300) return 'Very Unhealthy'
      return 'Hazardous'
    }
  })
  
  const indicatorPosition = computed(() => {
    if (props.type === 'indoor') {
      // 室内空气质量指示器位置 (1-6 映射到渐变条位置)
      const value = props.value || 0
      if (value < 1) return 0
      if (value > 6) return 100
      
      // 将1-6映射到渐变条的相应位置
      // 1(很差) -> 85% (对应危险区域)
      // 2(差) -> 70% (对应很不健康区域) 
      // 3(一般) -> 50% (对应不健康区域)
      // 4(好) -> 30% (对应敏感人群不健康区域)
      // 5(很好) -> 15% (对应缓和区域)
      // 6(最好) -> 5% (对应良好区域)
      const positionMap = {
        1: 85,
        2: 70, 
        3: 50,
        4: 30,
        5: 15,
        6: 5
      }
      return positionMap[value as keyof typeof positionMap] || 0
    } else {
      // 室外AQI指示器位置 (原有逻辑)
      const aqi = props.value
      let position = 0
      
      if (aqi <= 50) {
        // 0-50: 0-10% 区间
        position = (aqi / 50) * 10
      } else if (aqi <= 100) {
        // 51-100: 10-20% 区间
        position = 10 + ((aqi - 50) / 50) * 10
      } else if (aqi <= 150) {
        // 101-150: 20-30% 区间
        position = 20 + ((aqi - 100) / 50) * 10
      } else if (aqi <= 200) {
        // 151-200: 30-40% 区间
        position = 30 + ((aqi - 150) / 50) * 10
      } else if (aqi <= 300) {
        // 201-300: 40-60% 区间
        position = 40 + ((aqi - 200) / 100) * 20
      } else {
        // 301-500: 60-100% 区间
        position = 60 + ((Math.min(aqi, 500) - 300) / 200) * 40
      }
      
      return Math.max(Math.min(position, 100), 0)
    }
  })
  
  const description = computed(() => props.description)
  
  // 根据类型和值计算指示器颜色
  const indicatorColor = computed(() => {
    if (props.type === 'indoor') {
      // 室内空气质量颜色 (1-6)
      switch (props.value) {
        case 6: return '#4CAF50'  // 最好 - 绿色
        case 5: return '#8BC34A'  // 很好 - 浅绿色  
        case 4: return '#FFEB3B'  // 好 - 黄色
        case 3: return '#FF9800'  // 一般 - 橙色
        case 2: return '#F44336'  // 差 - 红色
        case 1: return '#795548'  // 很差 - 栗色
        default: return '#9E9E9E' // 无数据 - 灰色
      }
    } else {
      // 室外AQI颜色 (原有逻辑)
      if (props.value <= 50) return '#4CAF50'      // 绿色
      if (props.value <= 100) return '#FFEB3B'     // 黄色
      if (props.value <= 150) return '#FF9800'     // 橙色
      if (props.value <= 200) return '#F44336'     // 红色
      if (props.value <= 300) return '#9C27B0'     // 紫色
      return '#795548'                             // 栗色
    }
  })
  
  defineExpose({
    statusText,
    indicatorPosition
  })
  </script>
  
  <script lang="ts">
  import { defineComponent } from 'vue'
  
  export default defineComponent({
    name: 'AirQualityIndicator'
  })
  </script>
  
  <style scoped>
  .air-quality-container {   
    padding: 20px;  
    width: 280px;   
  }
  
  .status-text {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }
  
  .aqi-value {
    font-size: 48px;
    font-weight: bold;
    color: #00BCD4;
    margin-bottom: 16px;
    line-height: 1;
  }
  
  .quality-bar-container {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .quality-bar {
    position: relative;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .quality-gradient {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to right,
      /* 0-50: Good - 绿色 */
      #4CAF50 0%, #66BB6A 8%, #81C784 10%,
      
      /* 51-100: Moderate - 黄绿到黄色 */
      #CDDC39 10%, #FFEB3B 18%, #FFF176 20%,
      
      /* 101-150: Unhealthy for Sensitive - 黄到橙色 */
      #FFCC02 20%, #FF9800 28%, #FFB74D 30%,
      
      /* 151-200: Unhealthy - 橙到红色 */
      #FF6D00 30%, #F44336 38%, #E57373 40%,
      
      /* 201-300: Very Unhealthy - 红到紫色 */
      #AD1457 40%, #9C27B0 50%, #BA68C8 60%,
      
      /* 301-500: Hazardous - 深紫到栗色 */
      #6A1B9A 60%, #795548 80%, #8D6E63 100%
    );
    border-radius: 4px;
  }
  
  .quality-indicator {
    position: absolute;
    top: -2px;
    width: 12px;
    height: 12px;
    border: 2px solid white;
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: left 0.3s ease, background-color 0.3s ease;
  }
  
  .description-text {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 4px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 320px) {
    .air-quality-container {
      width: 100%;
      padding: 16px;
    }
    
    .aqi-value {
      font-size: 40px;
    }
  }
  
  /* Animation for value changes */
  .aqi-value {
    transition: color 0.3s ease;
  }
  </style>