/**
 * 用户访问控制相关类型定义
 */

// 用户角色枚举
export enum UserRole {
  ADMIN = 'Admin',
  MEMBER = 'Member',
  VIEWER = 'Viewer'
}

// 消息类型枚举
export enum MessageType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success'
}

// 用户信息类型
export interface UserInfo {
  id?: string;
  nickname: string;
  email?: string;
  role: string;
  timezone: string;
  avatar?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 设备位置数据类型
export interface LocationData {
  id?: string;
  name: string;
  locationManagement: string;
  location: string;
  roomCount: number;
  memberCount?: number;
  members: LocationMember[];
  createdAt?: string;
  updatedAt?: string;
}

// 位置成员类型
export interface LocationMember {
  id: string;
  userId?: string;
  locationId?: string;
  name: string;
  email?: string;
  role: UserRole | string;
  avatar?: string;
  joinedAt?: string;
}

// 群组信息类型
export interface GroupInfo {
  id: string;
  name: string;
  description?: string;
  memberCount: number;
  invitationCode?: string;
  createdAt: string;
  updatedAt?: string;
}

// 消息类型
export interface Message {
  id: string;
  title: string;
  content: string;
  type: MessageType;
  isRead: boolean;
  createdAt: string;
  updatedAt?: string;
}

// 位置选项类型
export interface LocationOption {
  id: number;
  name: string;
  checked: boolean;
}

// 地址建议类型
export interface AddressSuggestion {
  id: string;
  address: string;
  city: string;
  state: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// 表单数据类型
export interface AddLocationFormData {
  workspaceName: string;
  workspaceLocation: string;
  longitude?: number;
  latitude?: number;
  remark?: string;
  locations: string[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp?: number;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages?: number;
}

// 弹窗组件 Props 类型
export interface ModalProps {
  visible: boolean;
}

// 个人信息弹窗 Props
export interface PersonalInfoModalProps extends ModalProps {
  userInfo: UserInfo;
}

// 设备位置弹窗 Props
export interface DeviceLocationModalProps extends ModalProps {
  locationData: LocationData;
}

// 添加位置弹窗 Props
export interface AddLocationModalProps extends ModalProps {
  // 可以添加其他特定属性
}

// 加入群组弹窗 Props
export interface JoinGroupModalProps extends ModalProps {
  // 可以添加其他特定属性
}

// 事件类型定义
export interface UserAccessControlEvents {
  'update:visible': [value: boolean];
  'update': [data: any];
  'add': [data: any];
  'join': [code: string];
  'delete': [id: string];
}

// 组件状态类型
export interface ComponentState {
  loading: boolean;
  error: string | null;
  data: any;
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean;
  message: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any) => Promise<void>;
}

// 时区选项类型
export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

// 常用时区列表
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  { value: 'Shanghai', label: 'Shanghai (UTC+8)', offset: '+08:00' },
  { value: 'Beijing', label: 'Beijing (UTC+8)', offset: '+08:00' },
  { value: 'New York', label: 'New York (UTC-5)', offset: '-05:00' },
  { value: 'London', label: 'London (UTC+0)', offset: '+00:00' },
  { value: 'Tokyo', label: 'Tokyo (UTC+9)', offset: '+09:00' },
  { value: 'Sydney', label: 'Sydney (UTC+10)', offset: '+10:00' },
  { value: 'Los Angeles', label: 'Los Angeles (UTC-8)', offset: '-08:00' },
  { value: 'Paris', label: 'Paris (UTC+1)', offset: '+01:00' }
];

// 默认位置选项
export const DEFAULT_LOCATION_OPTIONS: LocationOption[] = [
  { id: 1, name: 'Living Room', checked: true },
  { id: 2, name: 'Master Bedroom', checked: true },
  { id: 3, name: 'Second Bedroom', checked: true },
  { id: 4, name: 'Office', checked: false },
  { id: 5, name: 'Conference Room', checked: false },
  { id: 6, name: 'Kitchen', checked: false },
  { id: 7, name: 'Bathroom', checked: false },
  { id: 8, name: 'Garage', checked: false }
];

// 用户角色选项
export const USER_ROLE_OPTIONS = [
  { value: UserRole.ADMIN, label: 'Administrator' },
  { value: UserRole.MEMBER, label: 'Member' },
  { value: UserRole.VIEWER, label: 'Viewer' }
];
