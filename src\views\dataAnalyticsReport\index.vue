<template>
    <div class="page-container">
      <h1 class="page-title">{{ t('airgle.dataAnalyticsReport.title') }}</h1>

      <!-- Data on filter life -->
      <div class="section-card">
        <div class="section-block">
          <h2 class="section-title">{{ t('airgle.dataAnalyticsReport.filterLife.title') }}</h2>
          <p class="section-description">{{ t('airgle.dataAnalyticsReport.filterLife.description') }}</p>

          <div class="radio-group">
            <label class="radio-item">
              <input
                type="radio"
                v-model="filterLifePeriod"
                value="1"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.filterLife.periods.1') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="filterLifePeriod"
                value="2"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.filterLife.periods.2') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="filterLifePeriod"
                value="3"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.filterLife.periods.3') }}</span>
            </label>
          </div>

          <div class="device-selector-container">
            <button
              @click="openDeviceSelector"
              class="select-device-btn"
            >
              {{ t('airgle.dataAnalyticsReport.selectDevice') }}
            </button>
            <div v-for="device in selectedFilterDevices" :key="device" class="device-tag">
              {{ device }}
              <button @click="removeFilterDevice(device)" class="remove-device-btn">
                <CloseOutlined class="remove-icon" />
              </button>
            </div>
          </div>
        </div>

        <!-- Data on air quality -->
        <div class="section-block">
          <h2 class="section-title">{{ t('airgle.dataAnalyticsReport.airQuality.title') }}</h2>
          <p class="section-description">{{ t('airgle.dataAnalyticsReport.airQuality.description') }}</p>

          <div class="pollution-section">
            <label class="pollution-label">{{ t('airgle.dataAnalyticsReport.airQuality.pollutionLevel') }}</label>
            <div class="pollution-levels">
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('1') ? 'pollution-level-selected excellent' : 'pollution-level-unselected excellent'
                ]"
                @click="togglePollutionLevel('1')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.1') }}
              </span>
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('2') ? 'pollution-level-selected good' : 'pollution-level-unselected good'
                ]"
                @click="togglePollutionLevel('2')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.2') }}
              </span>
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('3') ? 'pollution-level-selected light' : 'pollution-level-unselected light'
                ]"
                @click="togglePollutionLevel('3')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.3') }}
              </span>
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('4') ? 'pollution-level-selected moderate' : 'pollution-level-unselected moderate'
                ]"
                @click="togglePollutionLevel('4')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.4') }}
              </span>
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('5') ? 'pollution-level-selected heavy' : 'pollution-level-unselected heavy'
                ]"
                @click="togglePollutionLevel('5')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.5') }}
              </span>
              <span
                :class="[
                  'pollution-level',
                  selectedPollutionLevels.includes('6') ? 'pollution-level-selected severe' : 'pollution-level-unselected severe'
                ]"
                @click="togglePollutionLevel('6')"
              >
                {{ t('airgle.dataAnalyticsReport.airQuality.levels.6') }}
              </span>
            </div>
            <!-- 选中状态提示 -->
            <div v-if="selectedPollutionLevels.length > 0" class="selected-count">
              {{ t('airgle.dataAnalyticsReport.airQuality.selectedCount', { count: selectedPollutionLevels.length }) }}
            </div>
          </div>

          <div class="radio-group">
            <label class="radio-item">
              <input
                type="radio"
                v-model="airQualityPeriod"
                value="1"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.airQuality.periods.1') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="airQualityPeriod"
                value="3"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.airQuality.periods.3') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="airQualityPeriod"
                value="7"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.airQuality.periods.7') }}</span>
            </label>
          </div>
        </div>

        <!-- Activity Level -->
        <div class="section-block">
          <h2 class="section-title">{{ t('airgle.dataAnalyticsReport.activityLevel.title') }}</h2>
          <p class="section-description">{{ t('airgle.dataAnalyticsReport.activityLevel.description') }}</p>

          <div class="radio-group activity-group">
            <label class="radio-item">
              <input
                type="radio"
                v-model="activityLevel"
                value="1"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.activityLevel.levels.1') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="activityLevel"
                value="2"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.activityLevel.levels.2') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="activityLevel"
                value="3"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.activityLevel.levels.3') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="activityLevel"
                value="4"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.activityLevel.levels.4') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="activityLevel"
                value="5"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.activityLevel.levels.5') }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Export Parameters -->
      <div class="section-card">
        <h2 class="section-title">{{ t('airgle.dataAnalyticsReport.exportParams.title') }}</h2>
        <p class="section-description">{{ t('airgle.dataAnalyticsReport.exportParams.description') }}</p>

        <div class="format-selector">
          <label class="format-label">{{ t('airgle.dataAnalyticsReport.exportParams.dataFormat') }}</label>
          <select
            v-model="dataFormat"
            class="format-select"
          >
            <option value="filter-life">{{ t('airgle.dataAnalyticsReport.exportParams.formats.filter-life') }}</option>
            <option value="air-quality">{{ t('airgle.dataAnalyticsReport.exportParams.formats.air-quality') }}</option>
            <option value="activity-level">{{ t('airgle.dataAnalyticsReport.exportParams.formats.activity-level') }}</option>
          </select>
        </div>

        <!-- 经销商选择 -->
        <div class="provider-selection-container">
          <label class="provider-label">{{ t('airgle.dataAnalyticsReport.providerSelection.title') }}</label>
          <div class="provider-options">
            <label class="radio-item">
              <input
                type="radio"
                v-model="providerSelectionType"
                value="all"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.providerSelection.allProviders') }}</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                v-model="providerSelectionType"
                value="specific"
                class="radio-input"
              >
              <span class="radio-label">{{ t('airgle.dataAnalyticsReport.providerSelection.specificProviders') }}</span>
            </label>
          </div>

          <div v-if="providerSelectionType === 'specific'" class="provider-selector-container">
            <button
              @click="openProviderSelector"
              class="select-provider-btn"
            >
              {{ t('airgle.dataAnalyticsReport.providerSelection.selectProviders') }}
            </button>
            <div v-for="provider in selectedProviders" :key="provider.id" class="provider-tag">
              {{ provider.providerName }}
              <button @click="removeProvider(provider.id)" class="remove-provider-btn">
                <CloseOutlined class="remove-icon" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <button
          @click="clearAll"
          class="clear-btn"
        >
          {{ t('airgle.dataAnalyticsReport.actions.clearAll') }}
        </button>
        <button
          @click="exportData"
          :disabled="exporting"
          class="export-btn"
        >
          <DownloadOutlined v-if="!exporting" class="export-icon" />
          <CloseOutlined v-if="exporting" class="export-icon loading" />
          {{ exporting ? t('airgle.dataAnalyticsReport.actions.exporting') : t('airgle.dataAnalyticsReport.actions.exportData') }}
        </button>
      </div>

      <!-- Device Selection Modal -->
      <div v-if="showDeviceSelector" class="modal-overlay">
        <div class="modal-container">
          <h3 class="modal-title">{{ t('airgle.dataAnalyticsReport.deviceSelector.title') }}</h3>
          <div v-if="loading" class="loading-container">
            <span class="loading-text">{{ t('airgle.dataAnalyticsReport.deviceSelector.loading') }}</span>
          </div>
          <div v-else>
            <!-- 全选按钮 -->
            <div class="select-all-section">
              <label class="select-all-item">
                <input
                  type="checkbox"
                  :checked="tempSelectedDevices.length === availableDevices.length && availableDevices.length > 0"
                  @change="toggleSelectAll"
                  class="checkbox-input"
                >
                <span class="select-all-label">{{ t('airgle.dataAnalyticsReport.deviceSelector.selectAll') }}</span>
                <span class="select-count">{{ t('airgle.dataAnalyticsReport.deviceSelector.selectedCount', { selected: tempSelectedDevices.length, total: availableDevices.length }) }}</span>
              </label>
            </div>

            <!-- 设备列表 -->
            <div class="device-list">
              <label v-for="device in availableDevices" :key="device" class="device-item">
                <input
                  type="checkbox"
                  :value="device"
                  :checked="tempSelectedDevices.includes(device)"
                  @change="toggleDevice(device)"
                  class="checkbox-input"
                >
                <span class="device-name">{{ device }}</span>
              </label>
            </div>
          </div>
          <div class="modal-actions">
            <button
              @click="closeDeviceSelector"
              class="cancel-btn"
            >
              {{ t('airgle.dataAnalyticsReport.actions.cancel') }}
            </button>
            <button
              @click="confirmDeviceSelection"
              class="confirm-btn"
            >
              {{ t('airgle.dataAnalyticsReport.actions.confirm') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Provider Selection Modal -->
      <div v-if="showProviderSelector" class="modal-overlay">
        <div class="modal-container">
          <h3 class="modal-title">{{ t('airgle.dataAnalyticsReport.providerSelector.title') }}</h3>
          <div v-if="providerLoading" class="loading-container">
            <span class="loading-text">{{ t('airgle.dataAnalyticsReport.providerSelector.loading') }}</span>
          </div>
          <div v-else>
            <!-- 搜索框 -->
            <div class="search-section">
              <input
                v-model="providerSearchKeyword"
                type="text"
                :placeholder="t('airgle.dataAnalyticsReport.providerSelector.searchPlaceholder')"
                class="search-input"
                @input="filterProviders"
              >
            </div>

            <!-- 全选按钮 -->
            <div class="select-all-section">
              <label class="select-all-item">
                <input
                  type="checkbox"
                  :checked="tempSelectedProviders.length === filteredProviders.length && filteredProviders.length > 0"
                  @change="toggleSelectAllProviders"
                  class="checkbox-input"
                >
                <span class="select-all-label">{{ t('airgle.dataAnalyticsReport.providerSelector.selectAll') }}</span>
                <span class="select-count">{{ t('airgle.dataAnalyticsReport.providerSelector.selectedCount', { selected: tempSelectedProviders.length, total: filteredProviders.length }) }}</span>
              </label>
            </div>

            <!-- 经销商列表 -->
            <div class="provider-list">
              <label v-for="provider in filteredProviders" :key="provider.id" class="provider-item">
                <input
                  type="checkbox"
                  :value="provider"
                  :checked="tempSelectedProviders.some(p => p.id === provider.id)"
                  @change="toggleProvider(provider)"
                  class="checkbox-input"
                >
                <span class="provider-name">{{ provider.providerName }}</span>
                <span class="provider-info">{{ provider.saleCountry }} - {{ provider.saleLocation }}</span>
              </label>
            </div>

            <!-- 无搜索结果提示 -->
            <div v-if="filteredProviders.length === 0 && providerSearchKeyword" class="no-results">
              {{ t('airgle.dataAnalyticsReport.providerSelector.noResults') }}
            </div>
          </div>
          <div class="modal-actions">
            <button
              @click="closeProviderSelector"
              class="cancel-btn"
            >
              {{ t('airgle.dataAnalyticsReport.actions.cancel') }}
            </button>
            <button
              @click="confirmProviderSelection"
              class="confirm-btn"
            >
              {{ t('airgle.dataAnalyticsReport.actions.confirm') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { CloseOutlined, DownloadOutlined } from '@ant-design/icons-vue'
  import { getDeviceTypeList, exportDataAnalytics, type ExportDataAnalyticsParams } from './api'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { list as getProviderList } from '/@/views/aglProvider/AglProvider.api'

  const { createMessage } = useMessage()
  const { t } = useI18n()

  // Reactive data
  const filterLifePeriod = ref<string>('7')
  const airQualityPeriod = ref<string>('1')
  const activityLevel = ref<string>('1')
  const dataFormat = ref<string>('filter-life')

  // 污染等级选择
  const selectedPollutionLevels = ref<string[]>(['3', '4', '5', '6']) // 默认选择轻度污染及以上

  const selectedFilterDevices = ref<string[]>([])

  const showDeviceSelector = ref<boolean>(false)
  const loading = ref<boolean>(false)
  const exporting = ref<boolean>(false)

  const availableDevices = ref<string[]>([])
  const tempSelectedDevices = ref<string[]>([])

  // 经销商选择相关数据
  const providerSelectionType = ref<string>('all') // 'all' 或 'specific'
  const selectedProviders = ref<any[]>([])
  const showProviderSelector = ref<boolean>(false)
  const providerLoading = ref<boolean>(false)
  const availableProviders = ref<any[]>([])
  const tempSelectedProviders = ref<any[]>([])

  // 搜索功能相关数据
  const providerSearchKeyword = ref<string>('')
  const filteredProviders = ref<any[]>([])

  // Methods
  const loadDeviceTypes = async (): Promise<void> => {
    try {
      loading.value = true
      const deviceTypes = await getDeviceTypeList()
      availableDevices.value = deviceTypes || []

      // 默认全选所有设备
      selectedFilterDevices.value = [...availableDevices.value]
    } catch (error) {
      console.error('获取设备型号列表失败:', error)
      createMessage.error('获取设备型号列表失败')
      // 如果接口失败，使用默认数据
      availableDevices.value = [
        'A072',
        'AG2000',
        'AG2000双模版',
        'AG300',
        'AG3000 TEST',
        'AG600',
        'AG600 双模版',
        'AG800',
        'AG900',
        'BLE通用对接测试',
        '内衣呵护精灵-BLE',
        '测试'
      ]
      // 默认全选
      selectedFilterDevices.value = [...availableDevices.value]
    } finally {
      loading.value = false
    }
  }

  const removeFilterDevice = (device: string): void => {
    selectedFilterDevices.value = selectedFilterDevices.value.filter(d => d !== device)
  }

  const toggleDevice = (device: string): void => {
    const index = tempSelectedDevices.value.indexOf(device)
    if (index > -1) {
      tempSelectedDevices.value.splice(index, 1)
    } else {
      tempSelectedDevices.value.push(device)
    }
  }

  const toggleSelectAll = (): void => {
    if (tempSelectedDevices.value.length === availableDevices.value.length && availableDevices.value.length > 0) {
      tempSelectedDevices.value = []
    } else {
      tempSelectedDevices.value = [...availableDevices.value]
    }
  }

  const openDeviceSelector = (): void => {
    tempSelectedDevices.value = [...selectedFilterDevices.value]
    showDeviceSelector.value = true
  }

  const closeDeviceSelector = (): void => {
    showDeviceSelector.value = false
    tempSelectedDevices.value = []
  }

  const confirmDeviceSelection = (): void => {
    selectedFilterDevices.value = [...tempSelectedDevices.value]
    closeDeviceSelector()
  }

  const clearAll = (): void => {
    filterLifePeriod.value = '7'
    airQualityPeriod.value = '1'
    activityLevel.value = '1'
    dataFormat.value = 'filter-life'
    selectedPollutionLevels.value = ['3', '4', '5', '6'] // 重置为默认选择
    selectedFilterDevices.value = [...availableDevices.value] // 重置为全选
    providerSelectionType.value = 'all' // 重置为所有经销商
    selectedProviders.value = [] // 清空选中的经销商
  }

  const downloadFile = (data: Blob, fileName: string): void => {
    if (!data || data.size === 0) {
      createMessage.warning(t('airgle.dataAnalyticsReport.messages.downloadError'))
      return
    }

    // 兼容IE浏览器
    if ((window.navigator as any).msSaveBlob) {
      (window.navigator as any).msSaveBlob(new Blob([data]), fileName)
    } else {
      const url = window.URL.createObjectURL(new Blob([data]))
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }
  }

  const exportData = async (): Promise<void> => {
    if (selectedFilterDevices.value.length === 0) {
      createMessage.warning(t('airgle.dataAnalyticsReport.messages.selectDeviceWarning'))
      return
    }

    if (selectedPollutionLevels.value.length === 0) {
      createMessage.warning(t('airgle.dataAnalyticsReport.messages.selectPollutionLevelWarning'))
      return
    }

    if (providerSelectionType.value === 'specific' && selectedProviders.value.length === 0) {
      createMessage.warning(t('airgle.dataAnalyticsReport.messages.selectProviderWarning'))
      return
    }

    try {
      exporting.value = true
      
      // 显示导出开始提示
      createMessage.info('正在生成报告，请稍候...')

      // 构建导出参数
      const params: ExportDataAnalyticsParams = {
        format: dataFormat.value === 'filter-life' ? '1' :
                dataFormat.value === 'air-quality' ? '2' : '3',
        deviceTypes: selectedFilterDevices.value,
        filterLifetime: filterLifePeriod.value,
        activityFilterTime: activityLevel.value
      }

      // 添加经销商参数
      if (providerSelectionType.value === 'specific') {
        params.deviceSalerIds = selectedProviders.value.map(p => p.id)
      }

      // 添加空气质量参数
      if (dataFormat.value === 'air-quality') {
        params.airQuality = {
          pollutionLevel: selectedPollutionLevels.value,
          airFilterTime: airQualityPeriod.value === '1' ? '1' :
                        airQualityPeriod.value === '3' ? '3' : '7'
        }
      }

      const response = await exportDataAnalytics(params)

      // 检查响应是否为空
      if (!response || response.size === 0) {
        createMessage.error('导出失败：服务器返回空数据')
        return
      }

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10)
      const fileName = `数据分析报告_${timestamp}.xlsx`

      downloadFile(response, fileName)
      createMessage.success(t('airgle.dataAnalyticsReport.messages.exportSuccess'))

    } catch (error: any) {
      console.error('导出失败:', error)
      
      // 根据错误类型显示不同的错误信息
      if (error.message && error.message.includes('timeout')) {
        createMessage.error('导出超时，请尝试减少数据范围或稍后重试')
      } else if (error.message && error.message.includes('Network Error')) {
        createMessage.error('网络连接失败，请检查网络连接')
      } else {
        createMessage.error(t('airgle.dataAnalyticsReport.messages.exportError'))
      }
    } finally {
      exporting.value = false
    }
  }

  const togglePollutionLevel = (level: string): void => {
    const index = selectedPollutionLevels.value.indexOf(level)
    if (index > -1) {
      selectedPollutionLevels.value.splice(index, 1)
    } else {
      selectedPollutionLevels.value.push(level)
    }
  }

  // 经销商选择相关方法
  const loadProviders = async (): Promise<void> => {
    try {
      providerLoading.value = true
      const providers = await getProviderList({})
      availableProviders.value = providers || []
      filteredProviders.value = [...availableProviders.value] // 初始化过滤后的列表
    } catch (error) {
      console.error('获取经销商列表失败:', error)
      createMessage.error('获取经销商列表失败')
      availableProviders.value = []
      filteredProviders.value = []
    } finally {
      providerLoading.value = false
    }
  }

  const removeProvider = (providerId: string | number): void => {
    selectedProviders.value = selectedProviders.value.filter(p => p.id !== providerId)
  }

  const toggleProvider = (provider: any): void => {
    const index = tempSelectedProviders.value.findIndex(p => p.id === provider.id)
    if (index > -1) {
      tempSelectedProviders.value.splice(index, 1)
    } else {
      tempSelectedProviders.value.push(provider)
    }
  }

  const toggleSelectAllProviders = (): void => {
    if (tempSelectedProviders.value.length === filteredProviders.value.length && filteredProviders.value.length > 0) {
      tempSelectedProviders.value = []
    } else {
      tempSelectedProviders.value = [...filteredProviders.value]
    }
  }

  const openProviderSelector = (): void => {
    tempSelectedProviders.value = [...selectedProviders.value]
    providerSearchKeyword.value = '' // 清空搜索关键词
    filteredProviders.value = [...availableProviders.value] // 重置过滤列表
    showProviderSelector.value = true
  }

  const closeProviderSelector = (): void => {
    showProviderSelector.value = false
    tempSelectedProviders.value = []
    providerSearchKeyword.value = '' // 清空搜索关键词
  }

  const confirmProviderSelection = (): void => {
    selectedProviders.value = [...tempSelectedProviders.value]
    closeProviderSelector()
  }

  // 搜索功能
  const filterProviders = (): void => {
    if (!providerSearchKeyword.value) {
      filteredProviders.value = [...availableProviders.value]
      return
    }
    filteredProviders.value = availableProviders.value.filter(provider =>
      provider.providerName.toLowerCase().includes(providerSearchKeyword.value.toLowerCase()) ||
      provider.saleCountry.toLowerCase().includes(providerSearchKeyword.value.toLowerCase()) ||
      provider.saleLocation.toLowerCase().includes(providerSearchKeyword.value.toLowerCase())
    )
  }

  // 组件挂载时加载设备型号列表
  onMounted(() => {
    loadDeviceTypes()
    loadProviders() // 组件挂载时加载经销商列表
  })
  </script>

  <style scoped>
  /* 页面容器 */
  .page-container {
    padding: 0px 24px;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
  }

  /* 卡片样式 */
  .section-card {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 0.5rem;
  }

  /* 区块样式 */
  .section-block {
    padding-bottom: 1.5rem;
  }

  .section-block:last-child {
    padding-bottom: 0;
  }

  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
  }

  .section-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
  }

  /* 单选按钮组 */
  .radio-group {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .radio-item {
    display: flex;
    align-items: center;
  }

  .radio-input {
    width: 1rem;
    height: 1rem;
    color: #2563eb;
    border-color: #d1d5db;
  }

  .radio-input:focus {
    --tw-ring-color: #3b82f6;
  }

  .radio-label {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
  }

  /* 活动水平组特殊样式 */
  .activity-group {
    flex-wrap: wrap;
  }

  /* 设备选择器 */
  .device-selector-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .select-device-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .select-device-btn:hover {
    background-color: #f9fafb;
  }

  .device-tag {
    display: flex;
    align-items: center;
    background-color: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }

  .remove-device-btn {
    margin-left: 0.5rem;
    color: #2563eb;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .remove-device-btn:hover {
    color: #1d4ed8;
  }

  .remove-icon {
    width: 0.75rem;
    height: 0.75rem;
  }

  /* 经销商选择器 */
  .provider-selection-container {
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .provider-label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
    white-space: nowrap;
  }

  .provider-options {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
  }

  .provider-selector-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .select-provider-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .select-provider-btn:hover {
    background-color: #f9fafb;
  }

  .provider-tag {
    display: flex;
    align-items: center;
    background-color: #fef3c7;
    color: #92400e;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }

  .remove-provider-btn {
    margin-left: 0.5rem;
    color: #d97706;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .remove-provider-btn:hover {
    color: #b45309;
  }

  /* 经销商列表 */
  .provider-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 15rem;
    overflow-y: auto;
  }

  .provider-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
  }

  .provider-item:hover {
    background-color: #f9fafb;
  }

  .provider-name {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .provider-info {
    margin-left: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
  }

  /* 污染等级选择 */
  .pollution-section {
    margin-bottom: 1rem;
  }

  .pollution-label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
  }

  .pollution-levels {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .pollution-level {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid;
    user-select: none;
  }

  /* 污染等级颜色 */
  .pollution-level-unselected.excellent {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }

  .pollution-level-unselected.excellent:hover {
    background-color: #bbf7d0;
    border-color: #86efac;
  }

  .pollution-level-selected.excellent {
    background-color: #22c55e;
    color: white;
    border-color: #16a34a;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .pollution-level-unselected.good {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #fde68a;
  }

  .pollution-level-unselected.good:hover {
    background-color: #fde68a;
    border-color: #fcd34d;
  }

  .pollution-level-selected.good {
    background-color: #eab308;
    color: white;
    border-color: #ca8a04;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .pollution-level-unselected.light {
    background-color: #fed7aa;
    color: #c2410c;
    border-color: #fdba74;
  }

  .pollution-level-unselected.light:hover {
    background-color: #fdba74;
    border-color: #fb923c;
  }

  .pollution-level-selected.light {
    background-color: #f97316;
    color: white;
    border-color: #ea580c;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .pollution-level-unselected.moderate {
    background-color: #fecaca;
    color: #991b1b;
    border-color: #fca5a5;
  }

  .pollution-level-unselected.moderate:hover {
    background-color: #fca5a5;
    border-color: #f87171;
  }

  .pollution-level-selected.moderate {
    background-color: #ef4444;
    color: white;
    border-color: #dc2626;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .pollution-level-unselected.heavy {
    background-color: #e9d5ff;
    color: #7c3aed;
    border-color: #d8b4fe;
  }

  .pollution-level-unselected.heavy:hover {
    background-color: #d8b4fe;
    border-color: #c084fc;
  }

  .pollution-level-selected.heavy {
    background-color: #a855f7;
    color: white;
    border-color: #9333ea;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .pollution-level-unselected.severe {
    background-color: #795548ab;
    color:white;
    border-color: #795548dc;
  }

  .pollution-level-unselected.severe:hover {
    background-color: #795548dc;
    border-color: #795548dc;
  }

  .pollution-level-selected.severe {
    background-color:#795548f3;
    color: white;
    border-color: #795548fd;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .selected-count {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* 格式选择器 */
  .format-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .format-label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
  }

  .format-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
  }

  .format-select:focus {
    outline: none;
    --tw-ring-color: #3b82f6;
    border-color: #3b82f6;
  }

  /* 操作按钮 */
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
  }

  .clear-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    background: none;
    border: none;
    cursor: pointer;
  }

  .clear-btn:hover {
    color: #374151;
  }

  .export-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.5rem;
    color: white;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    background-color: #00A0E9;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .export-btn:hover:not(:disabled) {
    background-color: #0088C7;
  }

  .export-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .export-icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
  }

  .export-icon.loading {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* 模态框 */
  .modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }

  .modal-container {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    max-width: 28rem;
    width: 100%;
    margin: 0 1rem;
  }

  .modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .loading-container {
    text-align: center;
    padding: 1rem 0;
  }

  .loading-text {
    color: #6b7280;
  }

  /* 全选区域 */
  .select-all-section {
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .select-all-item {
    display: flex;
    align-items: center;
  }

  .select-all-label {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .select-count {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* 设备列表 */
  .device-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 15rem;
    overflow-y: auto;
  }

  .device-item {
    display: flex;
    align-items: center;
  }

  .device-name {
    margin-left: 0.5rem;
    font-size: 0.875rem;
  }

  .checkbox-input {
    width: 1rem;
    height: 1rem;
    color: #2563eb;
    border-color: #d1d5db;
    border-radius: 0.25rem;
  }

  .checkbox-input:focus {
    --tw-ring-color: #3b82f6;
  }

  /* 模态框操作按钮 */
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .cancel-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    background: none;
    border: none;
    cursor: pointer;
  }

  .cancel-btn:hover {
    color: #374151;
  }

  .confirm-btn {
    padding: 0.5rem 1rem;
    background-color: #2563eb;
    color: white;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
  }

  .confirm-btn:hover {
    background-color: #1d4ed8;
  }

  /* 搜索框 */
  .search-section {
    margin-bottom: 1rem;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
    outline: none;
  }

  .search-input:focus {
    --tw-ring-color: #3b82f6;
    border-color: #3b82f6;
  }

  /* 无搜索结果提示 */
  .no-results {
    text-align: center;
    padding: 1rem 0;
    color: #6b7280;
    font-size: 0.875rem;
  }

  /* 过渡动画 */
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade-enter-from, .fade-leave-to {
    opacity: 0;
  }
  </style>
