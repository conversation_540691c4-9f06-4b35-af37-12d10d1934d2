import { defHttp } from '/@/utils/http/axios'

// 定义工作空间记录接口
export interface WorkspaceRecord {
    id: string;
    workspaceName: string;
    workspaceLocation: string;
    longitude: number;
    latitude: number;
    remark: string;
}

// 定义工作空间列表响应接口
export interface WorkspaceListResponse {
    success: boolean;
    message: string;
    code: number;
    result: {
        records: WorkspaceRecord[];
        total: number;
        size: number;
        current: number;
        pages: number;
    };
    timestamp: number;
}

// 定义设备信息接口
export interface DeviceInfo {
    name: string;
    isOnline: string;
}

// 定义房间信息接口
export interface RoomInfo {
    roomName: string;
    devices: DeviceInfo[];
    totalDevices: number;
    roomAirQuality?: number;
}

// 定义室内数据响应接口
export interface IndoorDataResponse {
    success: boolean;
    message: string;
    code: number;
    result: RoomInfo[];
    timestamp: number;
    data?: RoomInfo[];
}

// 定义天气响应接口
export interface WeatherResponse {
    success: boolean;
    message: string;
    code: number;
    result: {
        air_quality: {
            aqi: string;
            pm25: string;
            co: string;
            o3: string;
        };
        current_weather: {
            temp: string;
            humidity: string;
            pressure: string;
        };
    };
    timestamp: number;
    air_quality?: {
        aqi: string;
        pm25: string;
        co: string;
        o3: string;
    };
    current_weather?: {
        temp: string;
        humidity: string;
        pressure: string;
    };
}

/**
 * 获取工作空间列表
 */
export const getWorkspaceList = (params: any = {}) => {
    return defHttp.get<WorkspaceListResponse>({
        url: '/airgle/aglWorkspace/list',
        params
    })
}

/**
 * 根据工作空间ID获取房间和设备信息
 */
export const getRoomAndDeviceByWorkspaceId = (workspaceId: string) => {
    return defHttp.get<IndoorDataResponse>({
        url: '/airgle/aglWorkspace/queryRoomAndDeviceByWorkspaceId',
        params: { workspaceId }
    })
}

/**
 * 获取当前天气信息
 */
export const getCurrentWeather = (lat: string, lon: string) => {
    return defHttp.get<WeatherResponse>({
        url: '/airgle/aglDevices/getCurrentWeather',
        params: { lat, lon }
    })
}