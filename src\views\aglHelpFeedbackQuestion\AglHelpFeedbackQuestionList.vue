<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item name="userName">
              <template #label><span :title="t('airgle.helpFeedback.userName')">{{ t('airgle.helpFeedback.userName') }}</span></template>
              <JRangeNumber v-model:value="queryParam.userName" class="query-group-cust"></JRangeNumber>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item name="feedbackCategory">
              <template #label><span :title="t('airgle.helpFeedback.feedbackCategory')">{{ t('airgle.helpFeedback.feedbackCategory') }}</span></template>
              <j-category-select :placeholder="t('airgle.helpFeedback.pleaseSelectFeedbackCategory')" v-model:value="queryParam.feedbackCategory" pcode="C04" @change="(value) => handleFormChange('feedbackCategory', value)" allow-clear />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="questionTime">
                <template #label><span :title="t('airgle.helpFeedback.questionTime')">{{ t('airgle.helpFeedback.questionTime') }}</span></template>
                <a-range-picker showTime value-format="YYYY-MM-DD HH:mm:ss" v-model:value="queryParam.questionTime" class="query-group-cust"/>
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="userPhone">
                <template #label><span :title="t('airgle.helpFeedback.userPhone')">{{ t('airgle.helpFeedback.userPhone') }}</span></template>
                <JRangeNumber v-model:value="queryParam.userPhone" class="query-group-cust"></JRangeNumber>
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="userEmail">
                <template #label><span :title="t('airgle.helpFeedback.userEmail')">{{ t('airgle.helpFeedback.userEmail') }}</span></template>
                <JRangeNumber v-model:value="queryParam.userEmail" class="query-group-cust"></JRangeNumber>
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="questionFrequency">
                <template #label><span :title="t('airgle.helpFeedback.questionFrequency')">{{ t('airgle.helpFeedback.questionFrequency') }}</span></template>
                <j-select-multiple :placeholder="t('airgle.helpFeedback.pleaseSelectQuestionFrequency')" v-model:value="queryParam.questionFrequency" dictCode="agl_question_frequency" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="isAgree">
                <template #label><span :title="t('airgle.helpFeedback.isAgree')">{{ t('airgle.helpFeedback.isAgree') }}</span></template>
                <j-select-multiple :placeholder="t('airgle.helpFeedback.pleaseSelectIsAgree')" v-model:value="queryParam.isAgree" dictCode="yn" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="handleStatus">
                <template #label><span :title="t('airgle.helpFeedback.handleStatus')">{{ t('airgle.helpFeedback.handleStatus') }}</span></template>
                <j-select-multiple :placeholder="t('airgle.helpFeedback.pleaseSelectHandleStatus')" v-model:value="queryParam.handleStatus" dictCode="agl_question_status" allow-clear />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">{{ t('common.query') }}</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">{{ t('common.reset') }}</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? t('common.collapse') : t('common.expand') }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'airgle:agl_help_feedback_question:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> {{ t('airgle.helpFeedback.add') }}</a-button>
        <a-button  type="primary" v-auth="'airgle:agl_help_feedback_question:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> {{ t('common.export') }}</a-button>
        <j-upload-button  type="primary" v-auth="'airgle:agl_help_feedback_question:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">{{ t('common.import') }}</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                {{ t('common.delete') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'airgle:agl_help_feedback_question:deleteBatch'">{{ t('common.batchOperation') }}
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex==='attach'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">{{ t('common.noFile') }}</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">{{ t('common.download') }}</a-button>
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AglHelpFeedbackQuestionModal ref="registerModal" @success="handleSuccess"></AglHelpFeedbackQuestionModal>
    
    <!-- 审核弹窗 -->
    <a-modal
      v-model:open="auditModalVisible"
      :title="t('airgle.helpFeedback.audit')"
      @ok="confirmAudit"
      @cancel="cancelAudit"
      :confirmLoading="auditLoading"
    >
      <a-form layout="vertical">
        <a-form-item :label="t('airgle.helpFeedback.handleRecord')" required>
          <a-textarea
            v-model:value="auditForm.handleRecord"
            :rows="4"
            :placeholder="t('airgle.helpFeedback.pleaseEnterHandleRecord')"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="airgle-aglHelpFeedbackQuestion" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './AglHelpFeedbackQuestion.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, handleFeedback } from './AglHelpFeedbackQuestion.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import AglHelpFeedbackQuestionModal from './components/AglHelpFeedbackQuestionModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JCategorySelect from '/@/components/Form/src/jeecg/components/JCategorySelect.vue';
  import { loadCategoryData } from '/@/api/common/api';
  import { getAuthCache, setAuthCache } from '/@/utils/auth';
  import { DB_DICT_DATA_KEY } from '/@/enums/cacheEnum';
  import { cloneDeep } from "lodash-es";
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  
  // 审核弹窗相关
  const auditModalVisible = ref<boolean>(false);
  const auditLoading = ref<boolean>(false);
  const currentRecord = ref<any>(null);
  const auditForm = reactive({
    handleRecord: ''
  });
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '用户问题反馈',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      actionColumn: {
        width: 180,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        let rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
      },
    },
    exportConfig: {
      name: "用户问题反馈",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 审核问题反馈
   */
  async function handleAudit(record) {
    currentRecord.value = record;
    auditForm.handleRecord = '';
    auditModalVisible.value = true;
  }
  
  /**
   * 确认审核
   */
  async function confirmAudit() {
    if (!auditForm.handleRecord.trim()) {
      createMessage.warning('请输入处理记录');
      return;
    }
    
    auditLoading.value = true;
    try {
      const params = {
        id: currentRecord.value.id,
        handleRecord: auditForm.handleRecord.trim()
      };
      
      console.log('审核参数:', params);
      console.log('当前记录:', currentRecord.value);
      console.log('API URL:', '/airgle/aglHelpFeedbackQuestion/handle');
      
      const response = await handleFeedback(params);
      console.log('审核响应:', response);
      
      if (response.success) {
        createMessage.success('审核成功');
        auditModalVisible.value = false;
        reload(); // 刷新表格
      } else {
        createMessage.error(response.message || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      console.error('错误详情:', error.response || error);
      createMessage.error('审核失败，请稍后重试');
    } finally {
      auditLoading.value = false;
    }
  }
  
  /**
   * 取消审核
   */
  function cancelAudit() {
    auditModalVisible.value = false;
    auditForm.handleRecord = '';
    currentRecord.value = null;
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    const actions = [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'airgle:agl_help_feedback_question:edit'
      }
    ];
    
    // 如果未处理，显示审核按钮
    if (record.handleStatus !== '1' && record.handleStatus !== 1) {
      actions.push({
        label: '审核',
        onClick: handleAudit.bind(null, record),
        auth: 'airgle:agl_help_feedback_question:edit'
      });
    }
    
    return actions;
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'airgle:agl_help_feedback_question:delete'
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  



  /**
   * form点击事件
   * @param value
   */
  function handleFormChange(key, value) {
    queryParam[key] = value;
  }
  
  /**
   * 初始化字典配置
   */
  function initDictConfig() {
    loadCategoryData({code:'C04'}).then((res) => {
      if (res) {
        const allDictDate = userStore.getAllDictItems;
        if(!allDictDate['C04']){
          userStore.setAllDictItems({...allDictDate,'C04':res});
        }
      }
    });
  }
  initDictConfig();
  
  let rangeField = 'questionTime,'
  
  /**
   * 设置范围查询条件
   */
  async function setRangeQuery(){
    let queryParamClone = cloneDeep(queryParam);
    if (rangeField) {
      let fieldsValue = rangeField.split(',');
      fieldsValue.forEach(item => {
        if (queryParamClone[item]) {
          let range = queryParamClone[item];
          queryParamClone[item+'_begin'] = range[0];
          queryParamClone[item+'_end'] = range[1];
          delete queryParamClone[item];
        } else {
          queryParamClone[item+'_begin'] = '';
          queryParamClone[item+'_end'] = '';
        }
      })
    }
    return queryParamClone;
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
