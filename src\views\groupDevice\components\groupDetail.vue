<!--
  群组详情组件
  功能：显示群组详细信息和设备图表，复用deviceManagement组件
  作者：开发团队
  创建时间：2024
-->
<template>
  <div class="group-detail">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <a @click="handleBack">Group Management</a>
        </a-breadcrumb-item>
        <a-breadcrumb-item>{{ groupData?.name || 'Group Detail' }}</a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 群组信息头部 -->
    <div class="group-header">
      <div class="group-info">
        <div class="group-title">
          <TeamOutlined class="group-icon" />
          <h2>{{ groupData?.name || 'Group Detail' }}</h2>
        </div>
        <div class="group-meta">
          <a-tag color="blue">{{ groupData?.deviceCount || 0 }} devices</a-tag>
          <a-tag :color="groupData?.status === 'Active' ? 'green' : 'red'">
            {{ groupData?.status || 'Unknown' }}
          </a-tag>
          <span class="location">{{ groupData?.location || 'Unknown Location' }}</span>
        </div>
      </div>
      <div class="group-actions">
        <a-button type="primary" @click="handlePublishData">
          <CloudUploadOutlined />
          Publish Data
        </a-button>
        <a-button @click="handleOpenControl">
          <SettingOutlined />
          Open Control
        </a-button>
      </div>
    </div>

    <!-- 状态栏 - 复用deviceManagement的topBar -->
    <div class="status-bar">
      <topBar ref="topBarRef" />
    </div>

    <!-- 主内容区域 -->
    <div class="group-content">
      <a-row :gutter="[16, 16]">
        <!-- 左侧设备列表 - 使用 groupDevice/components/deviceList.vue -->
        <a-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
          <deviceList
            :group-id="groupData?.id"
            @deviceSelected="handleDeviceSelected"
            v-loading="loading"
          />
        </a-col>
        <!-- 右侧图表区域 - 复用deviceManagement的rightEchart -->
        <a-col :xs="24" :sm="24" :md="16" :lg="18" :xl="18">
          <div v-if="!selectedDevice" class="empty-state">
            <a-empty description="Please select a device to view charts" />
          </div>
          <rightEchart
            v-else
            :product-name="selectedDevice.productName"
            :location-name="selectedDevice.locationName"
            :device-id="selectedDevice.deviceId"
            :selected-device="selectedDevice"
          />
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import { 
  TeamOutlined, 
  CloudUploadOutlined, 
  SettingOutlined 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 复用deviceManagement组件
import topBar from '../../deviceManagement/components/topBar.vue';
// 引入 deviceList 组件
import deviceList from './deviceList.vue';
import rightEchart from '../../deviceManagement/components/rightEchart.vue';

// 类型定义
interface GroupData {
  id: string;
  name: string;
  deviceCount: number;
  location: string;
  status: string;
  devices: string[];
  createdAt?: string;
}

interface DeviceData {
  id: string;
  productName: string;
  locationName: string;
  deviceId: string;
  signalStrength?: number;
  signalLevel?: string;
}

// Props和Emits
const props = defineProps<{
  groupData: GroupData | null;
}>();

const emit = defineEmits(['back']);

// 响应式数据
const loading = ref(false);
const selectedDevice = ref<DeviceData | null>(null);
const topBarRef = ref(null);
const leftListRef = ref(null);

// 方法定义
const handleBack = () => {
  emit('back');
};

const handleDeviceSelected = (deviceData: DeviceData, weatherData?: any) => {
  console.log('Device selected in group detail:', deviceData);
  selectedDevice.value = {
    ...deviceData,
    deviceId: deviceData.id
  };
  
  // 更新topBar的数据
  if (topBarRef.value && weatherData) {
    topBarRef.value.updateDeviceData?.(deviceData);
    topBarRef.value.updateWeatherData?.(weatherData);
  }
};

const handlePublishData = () => {
  console.log('Publishing group data...');
  message.info('Publishing group data...');
};

const handleOpenControl = () => {
  console.log('Opening group control...');
  message.info('Opening group control panel...');
};

const fetchGroupDevices = async () => {
  if (!props.groupData?.id) return;
  
  loading.value = true;
  try {
    // 这里可以调用API获取群组设备数据
    // 由于复用leftList组件，它会自动处理设备列表的获取
    console.log('Fetching devices for group:', props.groupData.id);
    
    // 如果需要过滤特定群组的设备，可以通过leftList的props传递
    if (leftListRef.value) {
      // leftListRef.value.fetchDeviceList();
    }
  } catch (error) {
    console.error('Failed to fetch group devices:', error);
    message.error('Failed to load group devices');
  } finally {
    loading.value = false;
  }
};

// 暴露方法
const refreshData = () => {
  fetchGroupDevices();
  if (leftListRef.value) {
    leftListRef.value.fetchDeviceList?.();
  }
};

defineExpose({
  refreshData
});

// 生命周期
onMounted(() => {
  console.log('Group detail mounted with data:', props.groupData);
  fetchGroupDevices();
});

// 监听群组数据变化
import { watch } from 'vue';
watch(() => props.groupData, (newData) => {
  if (newData) {
    console.log('Group data changed:', newData);
    selectedDevice.value = null; // 重置选中的设备
    fetchGroupDevices();
  }
}, { deep: true });
</script>

<style scoped lang="less">
.group-detail {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.breadcrumb-nav {
  margin-bottom: 16px;
  
  a {
    color: #1890ff;
    text-decoration: none;
    
    &:hover {
      color: #40a9ff;
    }
  }
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.group-info {
  flex: 1;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  
  .group-icon {
    font-size: 24px;
    color: #1890ff;
  }
  
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
  }
}

.group-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .location {
    color: #666;
    font-size: 14px;
  }
}

.group-actions {
  display: flex;
  gap: 12px;
}

.status-bar {
  margin-bottom: 16px;
}

.group-content {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

// 响应式设计
@media (max-width: 768px) {
  .group-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .group-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
