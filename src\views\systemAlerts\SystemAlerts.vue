<template>
    <div class="page-container">
      <h1 class="page-title">{{ t('routes.airgle.systemAlarm') }}</h1>

  <div class="system-alerts-page">
    <a-card class="sys-alert-card">
      <div class="display-center" v-if="loading">
        <a-spin size="large" />
      </div>
      <div class="alert-empty" v-else-if="!devices.length">
        <a-icon :component="AlertIcon" style="font-size: 48px; color: #e0e0e0;" />
        <div class="alert-text">{{ emptyText }}</div>
      </div>
      <div class="alert-list" v-else>
        <div v-for="device in paginatedDevices" :key="device.id" class="device-row">
          <div class="left">
            <span class="icon">
              <template v-if="device.status === 'error'">
                <ExclamationCircleOutlined style="color: red" />
              </template>
              <template v-else-if="device.status === 'warning'">
                <ExclamationCircleOutlined style="color: orange" />
              </template>
            </span>
            <span class="device-name">{{ device.name }}</span>
          </div>
          <div class="right">
            <a-badge class="right-badge" :status="device.status" />
            <RightOutlined style="color: gainsboro;font-size: 18px;" />
          </div>
        </div>
        <!-- 分页 -->
        <div class="paginationCounter justify-between" v-if="devices.length > 0">
          <div></div>
          <ConfigProvider :locale="enUS">
            <div class="flex-align">
              <div class="paginationCounter-item">
                {{ (currentPage - 1) * pageSize + 1 }} — {{ Math.min(currentPage * pageSize, devices.length) }} of {{ devices.length }} items
              </div>
              <Pagination
                v-model:current="currentPage"
                v-model:pageSize="pageSize"
                :total="devices.length"
                @change="handlePageChange"
                show-size-changer
                show-quick-jumper
                class="pagination"
              />
            </div>
          </ConfigProvider>
        </div>
      </div>
    </a-card>
  </div>
</div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { BellOutlined, ExclamationCircleOutlined, RightOutlined } from '@ant-design/icons-vue';
import { Pagination, ConfigProvider, Empty, Spin } from 'ant-design-vue';
import enUS from 'ant-design-vue/es/locale/en_US';
import { useI18n } from '/@/hooks/web/useI18n'

const { t } = useI18n()
console.log('Component setup starting...'); // Debug log

enUS.Pagination.items_per_page = '';
enUS.Pagination.jump_to = 'jump to page：';
enUS.Pagination.page = '';

const AlertIcon = BellOutlined;
const loading = ref(true);
const emptyText = ref('No Alerts');
const currentPage = ref(1);
const pageSize = ref(10);

// 初始化为空数组
const devices = ref([]);

// 生成测试数据的函数
const generateTestData = () => {
  console.log('Generating test data...'); // Debug log
  const testData = [
    { id: 1, name: 'AG2000-2', status: 'error' },
    { id: 2, name: 'AG900', status: 'warning' },
    ...Array.from({ length: 48 }, (_, i) => ({
      id: i + 3,
      name: `AG1000-${i + 3}`,
      status: i % 2 === 0 ? 'warning' : 'error',
    })),
  ];
  console.log('Generated test data:', testData); // Debug log
  return testData;
};

// 立即生成测试数据
console.log('Component script setup running...'); // Debug log
const testData = generateTestData();
console.log('Initial test data generated:', testData.length, 'items'); // Debug log

const paginatedDevices = computed(() => {
  console.log('Computing paginated devices...', {
    devices: devices.value,
    currentPage: currentPage.value,
    pageSize: pageSize.value
  }); // Debug log

  if (!devices.value || !devices.value.length) {
    console.log('No devices available'); // Debug log
    return [];
  }

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  const result = devices.value.slice(start, end);
  console.log('Paginated result:', result); // Debug log
  return result;
});

const handlePageChange = (page, size) => {
  console.log('Page change triggered:', { page, size }); // Debug log
  currentPage.value = page;
  pageSize.value = size;
};

// Watch for changes in devices array
watch(devices, (newVal) => {
  console.log('Devices array changed:', {
    length: newVal.length,
    isEmpty: newVal.length === 0
  });
}, { immediate: true });

onMounted(async () => {
  try {
    console.log('Component mounted, loading data...'); // Debug log
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    devices.value = generateTestData();
    console.log('Data loaded:', devices.value.length, 'items'); // Debug log
  } catch (error) {
    console.error('Error loading data:', error);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
  /* 页面容器 */
  .page-container {
    padding: 0px 24px;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
  }

.system-alerts-page {
  height: 80vh;
  background: none;
}

.sys-alert-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.alert-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.alert-text {
  margin-top: 16px;
  color: #bbb;
  font-size: 20px;
  font-weight: 500;
}

.alert-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.device-row {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
}

.device-row:hover {
  background-color: #f5f5f5;
}

.device-name {
  margin-left: 8px;
  font-weight: 500;
  color: #333;
}

.left {
  display: flex;
  align-items: center;
}

.icon {
  font-size: 16px;
}

.right {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.right-badge {
  position: absolute;
  top: -20px;
  right: -20px;
}

.paginationCounter {
  margin-top: auto;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.flex-align {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.paginationCounter-item {
  margin-right: 12px;
  color: #666;
}

.pagination {
  margin: 0;
}
</style>
