import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '串货记录id',
    align:"center",
    dataIndex: 'suspiciousId'
   },
   {
    title: '发送对象',
    align:"center",
    dataIndex: 'sentTo'
   },
   {
    title: '发送时间',
    align:"center",
    dataIndex: 'sentTime'
   },
   {
    title: '发送内容',
    align:"center",
    dataIndex: 'alertContent'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "串货记录id",
      field: 'suspiciousId',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "发送对象",
      field: 'sentTo',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "发送时间",
      field: 'sentTime',
      component: 'RangePicker',
      componentProps: {
         showTime: true,
         valueFormat: 'YYYY-MM-DD HH:mm:ss',
         placeholder: ['开始时间', '结束时间']
       },
      //colProps: {span: 6},
 	},
	{
      label: "发送内容",
      field: 'alertContent',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '串货记录id',
    field: 'suspiciousId',
    component: 'Input',
  },
  {
    label: '发送对象',
    field: 'sentTo',
    component: 'Input',
  },
  {
    label: '发送时间',
    field: 'sentTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '发送内容',
    field: 'alertContent',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  suspiciousId: {title: '串货记录id',order: 0,view: 'text', type: 'string',},
  sentTo: {title: '发送对象',order: 1,view: 'text', type: 'string',},
  sentTime: {title: '发送时间',order: 2,view: 'datetime-range', type: 'string',},
  alertContent: {title: '发送内容',order: 3,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}