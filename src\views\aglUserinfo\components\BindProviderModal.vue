<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    :title="'绑定经销商'" 
    :width="900"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <div class="bind-provider-modal">
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="经销商名称">
            <a-input v-model:value="searchForm.providerName" placeholder="请输入经销商名称" />
          </a-form-item>
          <a-form-item label="联系方式">
            <a-input v-model:value="searchForm.linkphone" placeholder="请输入联系方式" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- 列表区域 -->
      <div class="table-area">
        <BasicTable 
          @register="registerTable" 
          :row-selection="rowSelection"
          :scroll="{ y: 400 }"
        >
        </BasicTable>
      </div>
      
      <!-- 操作区域 -->
      <div class="action-area">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button 
            type="primary" 
            @click="handleBind"
            :disabled="!selectedProviderId"
            :loading="binding"
          >
            绑定
          </a-button>
        </a-space>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { page as getProviderList } from '/@/views/aglProvider/AglProvider.api';
  import { bindSaler } from '../AglUserinfo.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  
  const { createMessage } = useMessage();

  const emit = defineEmits(['success', 'register']);

  const searchForm = reactive({
    providerName: '',
    linkphone: ''
  });

  const selectedProviderId = ref<string | number | null>(null);
  const selectedProvider = ref<any>(null);
  const currentUserRecord = ref<any>(null);
  const binding = ref(false);

  // 列定义
  const columns: BasicColumn[] = [
    {
      title: '经销商名称',
      dataIndex: 'providerName',
      align: 'center',
    },
    {
      title: '联系方式',
      dataIndex: 'linkphone',
      align: 'center',
    },
    {
      title: '销售国家',
      dataIndex: 'saleCountry',
      align: 'center',
    },
    {
      title: '销售地区',
      dataIndex: 'saleLocation',
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'center',
    },
  ];

  // 表格配置
  const [registerTable, { reload }] = useTable({
    title: '',
    api: getProviderList,
    columns,
    canResize: false,
    pagination: true,
    showIndexColumn: false,
    useSearchForm: false,
    rowKey: 'id',  // 明确指定行键为id字段
    actionColumn: {
      width: 0,
    },
    beforeFetch: (params) => {
      return Object.assign(params, searchForm);
    },
  });

  // 单选配置
  const rowSelection = computed(() => ({
    type: 'radio',
    selectedRowKeys: selectedProviderId.value ? [selectedProviderId.value] : [],
    onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
      if (selectedRowKeys.length > 0) {
        selectedProviderId.value = selectedRowKeys[0];
        selectedProvider.value = selectedRows[0];
      } else {
        selectedProviderId.value = null;
        selectedProvider.value = null;
      }
    },
  }));

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    currentUserRecord.value = data?.record;
    
    // 重置搜索表单和选择
    searchForm.providerName = '';
    searchForm.linkphone = '';
    selectedProviderId.value = null;
    selectedProvider.value = null;
    
    // 刷新列表
    await reload();
  });

  // 搜索
  function handleSearch() {
    reload();
  }

  // 重置
  function handleReset() {
    searchForm.providerName = '';
    searchForm.linkphone = '';
    reload();
  }

  // 绑定
  async function handleBind() {
    if (!selectedProviderId.value || !currentUserRecord.value) {
      createMessage.warn('请选择要绑定的经销商');
      return;
    }

    console.log('当前用户记录:', currentUserRecord.value);
    console.log('选中的经销商ID:', selectedProviderId.value);
    
    const userId = currentUserRecord.value.systemUserId || currentUserRecord.value.id;
    if (!userId) {
      createMessage.warn('用户ID获取失败，请重试');
      return;
    }

    binding.value = true;
    try {
      const params = {
        salerId: selectedProviderId.value,
        userId: userId,
      };
      console.log('绑定参数:', params);
      
      await bindSaler(params);
      
      closeModal();
      emit('success');
    } catch (error) {
      console.error('绑定失败:', error);
    } finally {
      binding.value = false;
    }
  }

  // 取消
  function handleCancel() {
    closeModal();
  }
</script>

<style lang="less" scoped>
  .bind-provider-modal {
    .search-area {
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 4px;
    }

    .table-area {
      margin-bottom: 16px;
    }

    .action-area {
      text-align: right;
      padding: 16px 0;
      border-top: 1px solid #f0f0f0;
    }
  }
</style> 