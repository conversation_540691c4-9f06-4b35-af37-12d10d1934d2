export interface SettingItem {
  key: string;
  label: string;
  type: 'arrow' | 'switch' | 'select' | 'text';
  value?: any;
  options?: { label: string; value: any }[];
  unit?: string;
  disabled?: boolean;
}

export const columns = [
  {
    title: '设置项',
    dataIndex: 'label',
  },
  {
    title: '当前值',
    dataIndex: 'value',
  },
];

export const systemSettings: SettingItem[] = [
  { key: 'persona', label: '个人信息', value: '张三', type: 'text' },
  { key: 'account', label: '账号安全', value: '已绑定', type: 'text' },
  { key: 'device', label: '设备管理', value: '3台设备', type: 'text' },
  { key: 'touchTone', label: '按键音', value: '关闭', type: 'text' },
  { key: 'notification', label: '系统报警', value: '已开启', type: 'text' },
  { key: 'temperature', label: "t('systemSettings.temperature')", type: 'select', value: '℃', options: [ { label: '℃', value: '℃' }, { label: '℉', value: '℉' } ], unit: '℃' },
  { key: 'debug', label: "t('systemSettings.debug')", type: 'select', value: 'English', options: [ { label: 'English', value: 'English' }, { label: '中文', value: '中文' } ] },
  { key: 'features', label: "t('systemSettings.features')", type: 'text' },
  { key: 'about', label: "t('systemSettings.about')", type: 'text' },
  { key: 'privacy', label: "t('systemSettings.privacy')", type: 'text' },
  { key: 'policy', label: "t('systemSettings.policy')", type: 'text' },
  { key: 'network', label: "t('systemSettings.network')", type: 'text' },
  { key: 'cache', label: "t('systemSettings.cache')", type: 'text' },
]; 