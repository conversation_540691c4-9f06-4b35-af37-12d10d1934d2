<template>
  <div class="countInfo">
    <!-- 信息栏 -->
    <div class="display-center">
      <card :style="{ width: '100%', background: `url(${topbarBg}) no-repeat center center`, backgroundSize: '100% 100%' }">
        <div class="dashboard-panel">
          <img class="dashboard-panel-sun-img" :src="weatherIcon" :alt="weatherCondition" />
          <div class="dashboard-panel-content">
            <div class="dashboard-panel-content-up"> Local time </div>
            <div class="dashboard-panel-content-down">
              <div class="left">
                {{ currentTime }}
              </div>
              <div class="right"> {{ currentDate }} </div>
            </div>
            <div class="dashboard-panel-content-down">
              <div class="down-1"> {{ locationName }} </div>
              <div class="down-2 display-center">Outdoor</div>
            </div>
          </div>
          <div class="qualityList display-evenly">
            <div 
              v-for="item in qualityList" 
              :key="item.name" 
              class="qualityList-item"
            >
              <div class="qualityList-name">{{ item.name }}</div>
              <div class="qualityList-line">
                <div class="qualityList-line-value">{{ item.value }}</div>
                <div class="qualityList-line-title">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </card>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, defineExpose } from 'vue';
  import { Card } from 'ant-design-vue';
  
  // 导入静态资源
  import topbarBg from '/src/assets/device/topbar_bg.png';
  
  // 导入新的天气图标
  import qingtianIcon from '/src/assets/weather/qingtian.png';
  import yintianIcon from '/src/assets/weather/yintian.png';
  import duoyunzhuanqingIcon from '/src/assets/weather/duoyunzhuanqing.png';
  import qingwuIcon from '/src/assets/weather/qingwu.png';
  import zhongwuIcon from '/src/assets/weather/zhongwu.png';
  import xiaoyuIcon from '/src/assets/weather/xiaoyu.png';
  import zhenyuIcon from '/src/assets/weather/zhenyu.png';
  import dayuIcon from '/src/assets/weather/dayu.png';
  import leiyuIcon from '/src/assets/weather/leiyu.png';
  import leizhenyuIcon from '/src/assets/weather/leizhenyu.png';
  import leidianIcon from '/src/assets/weather/leidian.png';
  import jiangxueIcon from '/src/assets/weather/jiangxue.png';
  import zhenxueIcon from '/src/assets/weather/zhenxue.png';
  import dafengIcon from '/src/assets/weather/dafeng.png';
  
  // 保留旧图标作为备用
  import sunnyIcon from '/src/assets/weather/sunny.png';
  import cloudyIcon from '/src/assets/weather/cloudy.png';
  import overcastIcon from '/src/assets/weather/overcast.png';
  import rainIcon from '/src/assets/weather/rain.png';
  import snowIcon from '/src/assets/weather/snow.png';

  // 类型定义
  interface QualityItem {
    name: string;
    value: number | string;
    title: string;
  }

  interface DeviceData {
    aqi?: number;
    temperature?: number;
    humidity?: number;
    pm25?: number;
    co2?: number;
    locationName?: string;
  }

  interface WeatherData {
    temperature?: number;
    humidity?: number;
    location?: string;
    condition?: string;
  }

  // 常量定义
  const DEFAULT_WEATHER_ICON = qingtianIcon;
  const WEATHER_ICONS: Record<string, string> = {
    // 晴天类
    '晴': qingtianIcon,
    '晴天': qingtianIcon,
    'sunny': qingtianIcon,
    
    // 阴天类
    '阴': yintianIcon,
    '阴天': yintianIcon,
    'overcast': yintianIcon,
    
    // 多云类
    '多云': duoyunzhuanqingIcon,
    '多云转晴': duoyunzhuanqingIcon,
    'cloudy': duoyunzhuanqingIcon,
    
    // 雾霾类
    '雾': qingwuIcon,
    '轻雾': qingwuIcon,
    '雾霾': zhongwuIcon,
    '中雾': zhongwuIcon,
    '霾': zhongwuIcon,
    
    // 小雨类
    '小雨': xiaoyuIcon,
    '毛毛雨': xiaoyuIcon,
    'light rain': xiaoyuIcon,
    
    // 中雨类
    '中雨': zhenyuIcon,
    '阵雨': zhenyuIcon,
    'rain': zhenyuIcon,
    
    // 大雨类
    '大雨': dayuIcon,
    '暴雨': dayuIcon,
    'heavy rain': dayuIcon,
    
    // 雷雨类
    '雷雨': leiyuIcon,
    '雷阵雨': leizhenyuIcon,
    'thunderstorm': leidianIcon,
    
    // 雪类
    '雪': jiangxueIcon,
    '小雪': jiangxueIcon,
    '中雪': zhenxueIcon,
    '大雪': zhenxueIcon,
    'snow': jiangxueIcon,
    
    // 风类
    '大风': dafengIcon,
    '强风': dafengIcon,
    'windy': dafengIcon,
    
    // 备用旧图标（兼容性）
    'sunny_old': sunnyIcon,
    'cloudy_old': cloudyIcon,
    'overcast_old': overcastIcon,
    'rain_old': rainIcon,
    'snow_old': snowIcon
  };

  const DEFAULT_QUALITY_LIST: QualityItem[] = [
    { name: 'AQI(US)', value: '--', title: '' },
    { name: 'Temp.', value: '--', title: 'oF' },
    { name: 'RH.', value: '--', title: '%' },
    { name: 'PM2.5', value: '--', title: 'ug/m3' },
    { name: 'Co2', value: '--', title: 'ppm' },
  ];

  // 响应式数据
  const currentTime = ref('');
  const currentDate = ref('');
  const locationName = ref('--');
  const weatherIcon = ref(DEFAULT_WEATHER_ICON);
  const weatherCondition = ref('');
  const qualityList = ref<QualityItem[]>(DEFAULT_QUALITY_LIST);

  // 方法定义
  const formatTime = () => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString();
    currentDate.value = now.toLocaleDateString('en-US');
  };

  const getWeatherIcon = (condition: string): string => {
    weatherCondition.value = condition;
    return WEATHER_ICONS[condition] || DEFAULT_WEATHER_ICON;
  };

  const updateDeviceData = (deviceData: DeviceData) => {
    if (!deviceData) return;

    qualityList.value = [
      { name: 'AQI(US)', value: deviceData.aqi ?? '--', title: '' },
      { name: 'Temp.', value: deviceData.temperature ?? '--', title: 'oF' },
      { name: 'RH.', value: deviceData.humidity ?? '--', title: '%' },
      { name: 'PM2.5', value: deviceData.pm25 ?? '--', title: 'ug/m3' },
      { name: 'Co2', value: deviceData.co2 ?? '--', title: 'ppm' },
    ];

    if (deviceData.locationName) {
      locationName.value = deviceData.locationName;
    }
  };

  const updateWeatherData = (weatherData: WeatherData) => {
    if (!weatherData) return;

    if (weatherData.location) {
      locationName.value = weatherData.location;
    }

    if (weatherData.condition) {
      weatherIcon.value = getWeatherIcon(weatherData.condition);
    }

    // 更新温度和湿度
    if (weatherData.temperature !== undefined) {
      const tempItem = qualityList.value.find(item => item.name === 'Temp.');
      if (tempItem) {
        tempItem.value = weatherData.temperature;
      }
    }

    if (weatherData.humidity !== undefined) {
      const rhItem = qualityList.value.find(item => item.name === 'RH.');
      if (rhItem) {
        rhItem.value = weatherData.humidity;
      }
    }
  };

  // 生命周期钩子
  onMounted(() => {
    formatTime();
    const timeInterval = setInterval(formatTime, 1000);

    // 组件卸载时清除定时器
    return () => clearInterval(timeInterval);
  });

  // 暴露方法
  defineExpose({
    updateDeviceData,
    updateWeatherData,
  });
</script>
<style lang="less" scoped>
.countInfo{
  box-sizing: border-box;
}
.dashboard-panel {
  display: flex;
  align-items: center;
  .dashboard-panel-sun-img {
    width: 80px;
    height: 80px;   
    flex-shrink: 0;
  }
  .dashboard-panel-content {
    margin-left: 27px;
    .dashboard-panel-content-up {
      font-family: Arial;
      font-size: 16px;
      color: #000000;
    }
    .dashboard-panel-content-down {
      display: flex;
      align-items: center;
      .left {
        font-family: Arial;
        font-weight: bold;
        font-size: 30px;
        color: #000000;
      }
      .right {
        font-family: Arial;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        margin-left: 24px;
      }
    }
    .dashboard-panel-content-down {
      .down-1 {
        font-family: Arial;
        font-weight: 400;
        font-size: 16px;
        color: #010101;
      }
      .down-2 {       
        margin-left: 15px;
        width: 110px;
        height: 30px;
        background: #00a0e9;
        border-radius: 16px;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
  .qualityList {
    flex: 1;
    .qualityList-item {
      display: flex;
      flex-direction: column;
      .qualityList-name {
        font-family: Arial;
        font-weight: bold;
        font-size: 18px;
        color: #000000;
      }
      .qualityList-line {
        display: flex;
        align-items: baseline;
        .qualityList-line-value {
          font-family: Manrope;
          font-weight: 500;
          font-size: 30px;
          color: #00a0e9;
        }
        .qualityList-line-title {
          margin-top: 10px;
          font-family: Manrope;
          font-weight: 500;
          font-size: 15px;
          color: #00a0e9;
        }
      }
    }
  }
}
</style>
