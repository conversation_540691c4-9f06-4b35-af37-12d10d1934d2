import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

export const FILTER_OPTIONS = {
  ENVIRONMENT: [
    { label: t('airgle.device.filter.outdoor'), value: 'outdoor' },
    { label: t('airgle.device.filter.indoor'), value: 'indoor' },
  ],
  CONNECTION: [
    { label: t('airgle.common.all'), value: null },
    { label: t('airgle.common.online'), value: true },
    { label: t('airgle.common.offline'), value: false },
  ],
  DATA_PUBLICATION: [
    { label: t('airgle.common.yes'), value: true },
    { label: t('airgle.common.no'), value: false },
  ],
  MODE: [
    { label: t('airgle.device.filter.manual'), value: 'manual' },
    { label: t('airgle.device.filter.auto'), value: 'auto' },
    { label: t('airgle.device.filter.sleep'), value: 'sleep' },
  ],
};

export const DEBOUNCE_DELAY = 1000; 