<template>
  <div class="message-center">
    <!-- 顶部消息图标 -->
    <div class="top-icon">
      <MessageOutlined />
    </div>

    <!-- 标题 -->
    <h2 class="title">Message Center</h2>

    <a-dropdown class="settings-dropdown">
      <a-button type="text">
        <SettingOutlined :style="{ fontSize: '28px' }" />
      </a-button>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="markAllAsRead">
            <CheckOutlined />
            <span>全部已读</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- 消息类型1:通知公告2:系统消息3:设备位置 -->
    <div class="icon-buttons">
      <div class="icon-button" :class="{ active: msgCategory === 1 }" @click="changeMsgCategory(1)">
        <SoundOutlined />       
      </div>
      <div class="icon-button" :class="{ active: msgCategory === 2 }" @click="changeMsgCategory(2)">
        <BellOutlined />       
      </div>    

      <div class="icon-button" :class="{ active: msgCategory === 3 }" @click="changeMsgCategory(3)">
        <WarningOutlined />
      </div> 
    </div>

    <!-- 消息列表或空状态 -->
    <div class="content-area">
      <!-- 有消息时的列表 -->
      <div class="message-list" v-if="!loading && messages.length > 0">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="['message-item']"
          @click="handleMessageClick(message)"
        >
          <div class="message-content">
            <div class="message-header">
              <div class="message-title">
                <span class="message-text">{{ message.titile }}</span>
                <!-- 状态标签 -->
                <span v-if="message.readFlag === 0" class="status-tag unread-tag">未读</span>
                <span v-else class="status-tag read-tag">已读</span>
              </div>
              <!-- 优先级标签 -->
              <div v-if="message.priority" class="priority-tag" :class="`priority-${message.priority}`">
                {{ getPriorityText(message.priority) }}
              </div>
            </div>
            <div class="message-meta">
              <span class="sender">{{ message.sender || 'System' }}</span>
              <span class="time">{{ message.sendTime || '' }}</span>
            </div>
          </div>
          
          <!-- 未读指示器 -->
          <div v-if="message.readFlag === 0" class="unread-indicator"></div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <div class="empty-icon">
          <BellOutlined />
        </div>
        <p class="empty-text">No messages</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="large" />
      </div>
    </div>

    <!-- 消息详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="消息详情"
      :width="600"
      :footer="null"
    >
      <div v-if="selectedMessage" class="message-detail">
        <div class="detail-item">
          <label>状态：</label>
          <span :class="['status-badge', selectedMessage.readFlag === 0 ? 'unread-badge' : 'read-badge']">
            {{ selectedMessage.readFlag === 0 ? '未读' : '已读' }}
          </span>
        </div>
        <div class="detail-item">
          <label>标题：</label>
          <span>{{ selectedMessage.titile }}</span>
        </div>
        <div class="detail-item">
          <label>发送者：</label>
          <span>{{ selectedMessage.sender || 'System' }}</span>
        </div>
        <div class="detail-item">
          <label>发送时间：</label>
          <span>{{ selectedMessage.sendTime || '' }}</span>
        </div>
        <div class="detail-item">
          <label>优先级：</label>
          <span>{{ getPriorityText(selectedMessage.priority) || '普通' }}</span>
        </div>
        <div class="detail-item">
          <label>消息内容：</label>
          <div class="message-content-detail" v-html="selectedMessage.msgContent"></div>
        </div>
        <div class="detail-item" v-if="selectedMessage.msgAbstract">
          <label>消息摘要：</label>
          <span>{{ selectedMessage.msgAbstract }}</span>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { 
   BellOutlined,
   SoundOutlined,
   MessageOutlined,
   WarningOutlined,
   SettingOutlined,
   CheckOutlined
} from '@ant-design/icons-vue';
import {
  getMessages,
  updateMessageStatus,
  syncMessage,
  readAllMessages,
  type Message
} from '/@/views/userAccessControl/api';

// 响应式数据
const loading = ref(false);
const messages = ref<Message[]>([]);
const msgCategory = ref(1); // 默认显示系统消息
const detailModalVisible = ref(false);
const selectedMessage = ref<Message | null>(null);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: 0,
});

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const priorityMap = {
    'L': '低',
    'M': '中', 
    'H': '高'
  };
  return priorityMap[priority] || priority;
};

// 切换消息类型
const changeMsgCategory = (category: number) => {
  msgCategory.value = category;
  pagination.current = 1; // 重置分页
  loadMessages();
};

// 点击消息项
const handleMessageClick = async (msg: Message) => {
  try {
    // 调用更新消息状态接口
    await updateMessageStatus(msg.anntId);
    
    // 调用同步消息接口
    await syncMessage(msg.anntId);
    
    // 如果消息未读，更新本地状态
    if (msg.readFlag === 0) {
      const index = messages.value.findIndex(m => m.id === msg.id);
      if (index !== -1) {
        messages.value[index].readFlag = 1;
      }
    }
    
    // 显示消息详情模态框
    selectedMessage.value = msg;
    detailModalVisible.value = true;
    
  } catch (error) {
    console.error('处理消息点击失败:', error);
    message.error('处理消息失败');
  }
};

// 加载消息列表
const loadMessages = async () => {
  try {
    loading.value = true;
    const response = await getMessages({
      page: pagination.current,
      pageSize: pagination.pageSize,
      msgCategory: msgCategory.value,
    });  
    messages.value = response.records || [];
    pagination.total = response.total || 0;
  } catch (error) {
    console.error('获取消息列表失败:', error);
    message.error('获取消息列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadMessages();
});

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    await readAllMessages();
    await loadMessages(); // 重新加载消息列表
    message.success('所有消息已标记为已读');
  } catch (error) {
    console.error('标记所有消息为已读失败:', error);
    message.error('标记所有消息为已读失败');
  }
};
</script>

<style lang="less" scoped>
.message-center {
  width: 100%;
  background: #fff;
  padding: 40px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;

  .top-icon {
    margin-bottom: 20px;
    
    .anticon {
      font-size: 48px;
      color: #40a9ff;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 400;
    color: #000;
    margin-bottom: 40px;
  }

  .settings-dropdown {
    position: absolute;
    top: 3vw;
    right: 2vw;

    .ant-btn {
      padding: 0;
      background: none;
      border: none;
      outline: none;
      color: #8c8c8c;
      cursor: pointer;    

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .icon-buttons {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;

    .icon-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s;
      padding: 12px;
      border-radius: 8px;
      
      .anticon {
        font-size: 24px;
        color: #bfbfbf;
        margin-bottom: 8px;
      }

      .button-label {
        font-size: 12px;
        color: #8c8c8c;
        font-weight: 500;
      }

      &.active {
        .anticon {
          color: #40a9ff;
        }
        
        .button-label {
          color: #40a9ff;
        }
      }

      &:hover {
        background: #f5f5f5;
        
        .anticon {
          color: #40a9ff;
          transform: scale(1.1);
        }
        
        .button-label {
          color: #40a9ff;
        }
      }
    }
  }

  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .message-list {
      text-align: left;
      max-height: 400px;
      overflow-y: auto;

      .message-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative;

        &:hover {
          background: #fafafa;
        }

        .message-content {
          flex: 1;
          min-width: 0;

          .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
            gap: 12px;

            .message-title {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;

              .message-text {
                color: #262626;
                font-size: 14px;
                line-height: 1.5;
                word-break: break-word;
                font-weight: 500;
              }

              .status-tag {
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 12px;
                font-weight: 500;

                &.unread-tag {
                  background: #fff2f0;
                  color: #ff4d4f;
                  border: 1px solid #ffccc7;
                }

                &.read-tag {
                  max-width: 100px;
                  text-align: center;
                  background: #f6ffed;
                  color: #52c41a;
                  border: 1px solid #b7eb8f;
                }
              }
            }

            .priority-tag {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              white-space: nowrap;

              &.priority-L {
                background: #e6f7ff;
                color: #1890ff;
                border: 1px solid #91d5ff;
              }

              &.priority-M {
                background: #fff7e6;
                color: #fa8c16;
                border: 1px solid #ffd591;
              }

              &.priority-H {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
              }
            }
          }

          .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .sender {
              font-weight: 500;
              font-size: 13px;
              color: #595959;
            }
            
            .time {
              color: #8c8c8c;
              font-size: 12px;
            }
          }
        }

        .unread-indicator {
          flex-shrink: 0;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #52c41a;
          margin-top: 4px;
          box-shadow: 0 0 0 2px #fff, 0 0 0 3px #52c41a33;
          animation: pulse 2s infinite;
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #999;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        color: #d9d9d9;
      }

      p {
        font-size: 16px;
        margin-bottom: 16px;
      }
    }

    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动条样式 */
.message-list::-webkit-scrollbar {
  width: 4px;
}

.message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 消息详情模态框样式 */
.message-detail {
  .detail-item {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;

    label {
      font-weight: 500;
      color: #262626;
      min-width: 80px;
      margin-right: 12px;
    }

    span {
      color: #595959;
      flex: 1;
    }

    // 状态徽章
    .status-badge {
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;

      &.unread-badge {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
      }

      &.read-badge {
        max-width: 100px;
        text-align: center;
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
    }

    .message-content-detail {
      flex: 1;
      color: #595959;
      line-height: 1.6;
      padding: 12px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
      max-height: 200px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    }
  }
}
</style>
