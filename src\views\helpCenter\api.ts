/**
 * 帮助中心相关API
 */
import { defHttp } from '/@/utils/http/axios';

// API 接口地址
enum Api {
  // 帮助分类相关
  GET_HELP_CATEGORIES = '/airgle/aglHelpType/rootList',

  // 帮助信息相关
  GET_HELP_INFO_LIST = '/airgle/aglHelpType/childList',
  GET_HELP_INFO_DETAIL = '/airgle/aglHelpInfo/list',

  // 反馈相关
  SUBMIT_FEEDBACK = '/airgle/aglHelpInfo/isHelpful',
}

// 帮助分类接口
export interface HelpCategory {
  id: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  sysOrgCode?: string;
  pid?: string;
  hasChild?: string;
  typeName: string;
  hasChild_dictText?: string;
}

// 帮助信息接口
export interface HelpInfo {
  id: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  sysOrgCode?: string;
  helpTitle: string;
  helpType: string;
  helpContent: string;
  typeName?: string;
}

// API 响应接口
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

// 分页响应接口
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  orders?: Array<{
    column: string;
    asc: boolean;
  }>;
  optimizeCountSql?: boolean;
  searchCount?: boolean;
  optimizeJoinOfCountSql?: boolean;
  maxLimit?: number;
  countId?: string;
}

/**
 * 获取帮助分类列表
 */
export const getHelpCategories = () => {
  return defHttp.get<PageResult<HelpCategory>>({
    url: Api.GET_HELP_CATEGORIES
  });
};

/**
 * 获取帮助信息列表
 * @param pid 父级分类ID
 * @param typeName 搜索关键词
 */
export const getHelpInfoList = (pid: string, typeName?: string) => {
  return defHttp.get<PageResult<HelpInfo>>({
    url: Api.GET_HELP_INFO_LIST,
    params: {
      pid: pid,
      ...(typeName && { typeName: typeName })
    }
  });
};

/**
 * 获取帮助信息详情
 * @param helpType 帮助信息ID
 */
export const getHelpInfoDetail = (id: string) => {
  return defHttp.get<PageResult<HelpInfo>>({
    url: Api.GET_HELP_INFO_DETAIL,
    params: {
      helpTitle: '',
      pageNo: 1,
      pageSize: 1,
      helpType: id
    }
  });
};

/**
 * 提交帮助反馈
 * @param helpInfoId 帮助信息ID
 * @param type 反馈类型 (1: 有帮助, 0: 无帮助)
 */
export const submitHelpFeedback = (helpInfoId: string, type: number) => {
  return defHttp.post<any>({
    url: Api.SUBMIT_FEEDBACK,
    params: {
      helpInfoId: helpInfoId,
      type: type
    }
  });
};
