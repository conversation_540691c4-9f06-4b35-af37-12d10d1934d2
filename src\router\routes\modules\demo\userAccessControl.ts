import type { AppRouteModule } from '/@/router/types';
import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const userAccessControlDemo: AppRouteModule = {
  path: '/demo-user-access',
  name: 'DemoUserAccess',
  component: LAYOUT,
  redirect: '/demo-user-access/index',
  meta: {
    orderNo: 35,
    icon: 'ion:person-circle-outline',
    title: '用户访问控制演示',
  },
  children: [
    {
      path: 'index',
      name: 'UserAccessControlDemo',
      component: () => import('/@/views/demo/userAccessControl/index.vue'),
      meta: {
        title: '用户访问控制演示',
      },
    },
    {
      path: 'message-center',
      name: 'MessageCenterDemo',
      component: () => import('/@/views/demo/messageCenter/index.vue'),
      meta: {
        title: '消息中心演示',
      },
    },
  ],
};

export default userAccessControlDemo;
