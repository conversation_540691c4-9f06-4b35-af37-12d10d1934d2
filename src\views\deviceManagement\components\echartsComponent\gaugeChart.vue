<template>
  <div class="air-quality-gauge">
    <div class="gauge-container">
      <div ref="gaugeRef" class="gauge-chart"></div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 空气质量数值 (0-6)
  value: {
    type: Number,
    default: 4
  },
  // 文本配置
  poorLabel: {
    type: String,
    default: '差'
  },
  excellentLabel: {
    type: String,
    default: '优'
  },
  detailText: {
    type: String,
    default: '室内空气质量'
  }
})




const gaugeRef = ref(null)
let myChart = null

// 根据数值生成饼图配置
const getOptionByValue = (value) => {
  value = Math.max(0, Math.min(6, value));

  // 用 startAngle: 270，使 index 0 从左下开始，顺时针排布
  const fillOrder = [0, 1, 2, 3, 4, 5];
  const hiddenIndex = [6, 7]; // 底部两个隐藏块

  return {
    backgroundColor: '#00000000',
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        center: ['50%', '50%'],
        startAngle: 225,
        label: { show: false },
        labelLine: { show: false },
        data: Array.from({ length: 8 }, (_, i) => {
          const isHidden = hiddenIndex.includes(i);
          const fillIndex = fillOrder.indexOf(i);
          const isActive = fillIndex !== -1 && fillIndex < value;
          return {
            value: 1,
            itemStyle: {
              color: isHidden
                ? 'transparent'
                : isActive
                  ? '#33a6f3'
                  : '#D3D3D3',
              borderColor: isHidden ? 'transparent' : '#f5f9fe',
              borderWidth: isHidden ? 0 : 4
            }
          };
        })
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: '40%',
        style: {
          text: value === 0
            ? '暂无数据'
            : value <= 2
              ? '严重污染'
              : value <= 4
                ? '轻度污染'
                : '空气良好',
          fontSize: 24,
          fontWeight: 'bold',
          fill: '#000'
        }
      },
      {
        type: 'text',
        left: 'center',
        top: '52%',
        style: {
          text: '室内空气质量',
          fontSize: 16,
          fill: '#888'
        }
      },
      {
        type: 'text',
        left: '10%',
        top: '78%',
        style: {
          text: '差',
          fontSize: 16,
          fill: '#999'
        }
      },
      {
        type: 'text',
        right: '10%',
        top: '78%',
        style: {
          text: '优',
          fontSize: 16,
          fill: '#999'
        }
      }
    ]
  };
}

const renderGauge = () => {
  if (!gaugeRef.value) return
  if (!myChart) {
    myChart = echarts.init(gaugeRef.value)
  }
  const option = getOptionByValue(props.value)
  myChart.setOption(option)
}

// Watch for data changes to update the chart
watch([
  () => props.value,
  () => props.poorLabel,
  () => props.excellentLabel,
  () => props.detailText
], () => {
  renderGauge()
}, { deep: true })

let resizeObserver = null

onMounted(() => {
  renderGauge()
  resizeObserver = new ResizeObserver(() => {
    myChart?.resize()
  })
  resizeObserver.observe(gaugeRef.value)
})

onUnmounted(() => {
  resizeObserver?.disconnect()
  myChart?.dispose()
  myChart = null
})
</script>

<style scoped lang="less">
.air-quality-gauge {

  .gauge-container {
    display: flex;
    height: 64vh;
    justify-content: center;
    align-items: center;

    .gauge-chart {
      width: 584px;
      height: 534px;
    }
  }
}
</style>
