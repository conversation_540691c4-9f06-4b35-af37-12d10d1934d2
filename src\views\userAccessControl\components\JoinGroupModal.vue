<!--
  加入群组弹窗组件
  功能：通过邀请码加入群组
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="加入群组"
    :width="450"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="join-group-modal">
      <!-- 图标 -->
      <div class="icon-section">
        <div class="group-icon">
          <TeamOutlined />
          <PlusOutlined class="plus-icon" />
        </div>
      </div>

      <!-- 邀请码输入 -->
      <div class="input-section">
        <a-input
          v-model:value="invitationCode"
          placeholder="请输入邀请码"
          size="large"
          class="invitation-input"
        />
      </div>

      <!-- 加入按钮 -->
      <div class="action-section">
        <a-button 
          type="primary" 
          size="large" 
          block
          @click="handleJoin"
          :loading="joining"
        >
          加入
        </a-button>
      </div>

      <!-- 提示信息 -->
      <div class="tip-section">
        <p>
          请联系管理员为您创建邀请<br>
          (设备位置设置 > 添加成员)
        </p>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { TeamOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { acceptInvite } from '/@/views/aglWorkspace/AglWorkspace.api';

// Props 定义
interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'join': [invitationCode: string];
}>();

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const invitationCode = ref('');
const joining = ref(false);

// 方法定义
const handleCancel = () => {
  modalVisible.value = false;
  invitationCode.value = '';
};

const handleJoin = async () => {
  if (!invitationCode.value.trim()) {
    message.error('请输入邀请码');
    return;
  }

  joining.value = true;
  
  try {
    // 准备API请求参数
    const params = {
      inviteCode: invitationCode.value.trim()
    };

    console.log('接受邀请参数:', params);

    // 调用接受邀请API
    const result = await acceptInvite(params);

    if (result) {
      emit('join', invitationCode.value);
      message.success('成功加入群组！');
      modalVisible.value = false;
      invitationCode.value = '';
      
      // 可以在这里处理返回的群组信息
      console.log('加入群组结果:', result);
    }
    
  } catch (error: any) {
    console.error('加入群组失败:', error);
    
    // 根据错误类型显示不同的错误消息
    if (error?.response?.status === 400) {
      message.error('邀请码无效或已过期');
    } else if (error?.response?.status === 404) {
      message.error('邀请码不存在');
    } else if (error?.response?.status === 409) {
      message.error('您已经是该群组的成员');
    } 
  } finally {
    joining.value = false;
  }
};
</script>

<style scoped lang="less">
.join-group-modal {
  text-align: center;
}

.icon-section {
  margin-bottom: 32px;
}

.group-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff7a45, #ff4d4f);
  border-radius: 50%;
  color: white;
  font-size: 32px;
}

.plus-icon {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
}

.input-section {
  margin-bottom: 24px;
}

.invitation-input {
  text-align: center;
  font-size: 16px;
  
  :deep(.ant-input) {
    text-align: center;
    border-radius: 8px;
    padding: 12px 16px;
  }
}

.action-section {
  margin-bottom: 32px;
}

.action-section .ant-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

.tip-section {
  p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .join-group-modal {
    padding: 20px 10px 10px;
  }
  
  .group-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .plus-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
</style>
