import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '经销商名称',
    align:"center",
    sorter: true,
    dataIndex: 'providerName'
   },
   {
    title: '联系方式',
    align:"center",
    dataIndex: 'linkphone'
   },
   {
    title: '销售国家',
    align:"center",
    sorter: true,
    dataIndex: 'saleCountry'
   },
   {
    title: '销售地区',
    align:"center",
    sorter: true,
    dataIndex: 'saleLocation'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "经销商名称",
      field: 'providerName',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "联系方式",
      field: 'linkphone',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "销售国家",
      field: 'saleCountry',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "销售地区",
      field: 'saleLocation',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '经销商名称',
    field: 'providerName',
    component: 'Input',
  },
  {
    label: '联系方式',
    field: 'linkphone',
    component: 'Input',
  },
  {
    label: '销售国家',
    field: 'saleCountry',
    component: 'Input',
  },
  {
    label: '销售地区',
    field: 'saleLocation',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  providerName: {title: '经销商名称',order: 0,view: 'text', type: 'string',},
  linkphone: {title: '联系方式',order: 1,view: 'text', type: 'string',},
  saleCountry: {title: '销售国家',order: 2,view: 'text', type: 'string',},
  saleLocation: {title: '销售地区',order: 3,view: 'text', type: 'string',},
  remark: {title: '备注',order: 4,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}