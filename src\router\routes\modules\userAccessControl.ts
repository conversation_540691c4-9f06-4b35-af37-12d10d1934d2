import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const userAccessControl: AppRouteModule = {
  path: '/user-access-control',
  name: 'UserAccessControl',
  component: LAYOUT,
  redirect: '/user-access-control/index',
  meta: {
    orderNo: 30,
    icon: 'ion:person-outline',
    title: t('routes.user.accessControl'),
  },
  children: [
    {
      path: 'index',
      name: 'UserAccessControlPage',
      component: () => import('/@/views/userAccessControl/index.vue'),
      meta: {
        title: t('routes.user.accessControl'),
      },
    },
  ],
};

export default userAccessControl;
