/**
 * Google Maps API管理工具
 * 确保Google Maps API只加载一次，避免重复加载错误
 */

const GOOGLE_MAPS_API_URL = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCij3LNzDmUSlqvnjN554RXMsQceWavjNI&libraries=places,drawing&language=zh-CN&v=weekly';

interface GoogleMapsService {
  map: any;
  geocoder: any;
  placesService: any;
  marker: any;
}

class GoogleMapsManager {
  private loadPromise: Promise<void> | null = null;
  private isLoaded = false;

  /**
   * 加载Google Maps API
   * 如果已经加载过，直接返回Promise
   */
  async loadGoogleMaps(): Promise<void> {
    // 检查是否已经加载过
    if (this.isLoaded || (window as any).google?.maps) {
      this.isLoaded = true;
      return Promise.resolve();
    }

    // 如果正在加载中，返回当前的加载Promise
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // 开始加载
    this.loadPromise = new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = GOOGLE_MAPS_API_URL;
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        this.isLoaded = true;
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error('Google Maps API 加载失败'));
      };
      
      document.head.appendChild(script);
    });

    return this.loadPromise;
  }

  /**
   * 检查Google Maps API是否已加载
   */
  isGoogleMapsLoaded(): boolean {
    return this.isLoaded && !!(window as any).google?.maps;
  }

  /**
   * 创建地图实例
   */
  createMap(container: HTMLElement, options: any): any {
    if (!this.isGoogleMapsLoaded()) {
      throw new Error('Google Maps API 未加载');
    }
    
    const google = (window as any).google;
    return new google.maps.Map(container, options);
  }

  /**
   * 创建地理编码器
   */
  createGeocoder(): any {
    if (!this.isGoogleMapsLoaded()) {
      throw new Error('Google Maps API 未加载');
    }
    
    const google = (window as any).google;
    return new google.maps.Geocoder();
  }

  /**
   * 创建Places服务
   */
  createPlacesService(map: any): any {
    if (!this.isGoogleMapsLoaded()) {
      throw new Error('Google Maps API 未加载');
    }
    
    const google = (window as any).google;
    return new google.maps.places.PlacesService(map);
  }

  /**
   * 创建标记
   */
  createMarker(options: any): any {
    if (!this.isGoogleMapsLoaded()) {
      throw new Error('Google Maps API 未加载');
    }
    
    const google = (window as any).google;
    return new google.maps.Marker(options);
  }

  /**
   * 获取Google Maps对象
   */
  getGoogleMaps(): any {
    if (!this.isGoogleMapsLoaded()) {
      throw new Error('Google Maps API 未加载');
    }
    
    return (window as any).google.maps;
  }
}

// 导出单例实例
export const googleMapsManager = new GoogleMapsManager();

// 导出便捷方法
export const loadGoogleMaps = () => googleMapsManager.loadGoogleMaps();
export const isGoogleMapsLoaded = () => googleMapsManager.isGoogleMapsLoaded();
export const createMap = (container: HTMLElement, options: any) => googleMapsManager.createMap(container, options);
export const createGeocoder = () => googleMapsManager.createGeocoder();
export const createPlacesService = (map: any) => googleMapsManager.createPlacesService(map);
export const createMarker = (options: any) => googleMapsManager.createMarker(options);
export const getGoogleMaps = () => googleMapsManager.getGoogleMaps(); 