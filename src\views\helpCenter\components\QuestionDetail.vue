<template>
  <div class="question-detail">
    <!-- Header -->
    <div class="detail-header">
      <Button type="text" @click="$emit('back')" class="mb-4">
        <LeftOutlined />
        返回FAQ
      </Button>
      <h2 class="text-xl font-semibold mb-4">{{ questionData?.helpTitle }}</h2>
    </div>

    <!-- Content -->
    <div class="detail-content">
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="prose max-w-none">
          <div v-if="questionData?.helpContent" v-html="formatContent(questionData.helpContent)"></div>
          <div v-else class="text-gray-500 italic">此问题暂无详细内容。</div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="detail-actions mt-6">
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium mb-4">
          {{ hasFeedback ? '感谢您的反馈！' : '此内容对您有帮助吗？' }}
        </h3>
        
        <!-- 反馈结果显示 -->
        <div v-if="hasFeedback" class="feedback-result mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="flex items-center gap-2 text-green-700">
            <LikeOutlined v-if="feedbackType === 'helpful'" />
            <DislikeOutlined v-else />
            <span>您已标记此内容为{{ feedbackType === 'helpful' ? '有帮助' : '无帮助' }}</span>
          </div>
        </div>
        
        <!-- 反馈按钮 -->
        <div class="flex gap-4 mb-4">
          <Button 
            :type="feedbackType === 'helpful' ? 'primary' : 'default'"
            :disabled="hasFeedback"
            @click="handleFeedback('helpful')"
          >
            <LikeOutlined />
            有帮助
          </Button>
          <Button 
            :type="feedbackType === 'not-helpful' ? 'primary' : 'default'"
            :disabled="hasFeedback"
            @click="handleFeedback('not-helpful')"
          >
            <DislikeOutlined />
            无帮助
          </Button>
        </div>
        
        <div class="border-t pt-4">
          <h4 class="font-medium mb-2">仍需要帮助？</h4>
          <Button type="default" @click="handleContactSupport">
            <MessageOutlined />
            联系支持
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Button, message } from 'ant-design-vue';
import { 
  LeftOutlined, 
  LikeOutlined, 
  DislikeOutlined, 
  MessageOutlined 
} from '@ant-design/icons-vue';
import { getHelpInfoDetail, submitHelpFeedback, type HelpInfo } from '../api';

interface Props {
  questionId?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  back: [];
}>();

const questionData = ref<HelpInfo | null>(null);
const hasFeedback = ref(false); // 是否已经反馈过
const feedbackType = ref<'helpful' | 'not-helpful' | null>(null); // 反馈类型

// 监听问题ID变化，获取详情
watch(() => props.questionId, async (newId) => {
  if (newId) {
    try {
      const response = await getHelpInfoDetail(newId);
      if (response.records && response.records.length > 0) {
        questionData.value = response.records[0];
      }
    } catch (error) {
      console.error('Error fetching question details:', error);
      message.error('Error loading question details');
    }
  } else {
    questionData.value = null;
  }
  
  // 重置反馈状态
  hasFeedback.value = false;
  feedbackType.value = null;
}, { immediate: true });

// 格式化内容，将换行符转换为HTML
const formatContent = (content: string) => {
  if (!content) return '';
  
  // 简单的文本格式化：将换行符转换为<br>标签
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>'); // 斜体
};

// 处理反馈
const handleFeedback = async (type: 'helpful' | 'not-helpful') => {
  if (!questionData.value?.id) {
    message.error('无法提交反馈：问题信息未加载');
    return;
  }

  // 如果已经反馈过，不允许再次反馈
  if (hasFeedback.value) {
    message.warning('您已经对此内容进行过反馈，无法重复操作');
    return;
  }

  try {
    const feedbackValue = type === 'helpful' ? 1 : 0;
    await submitHelpFeedback(questionData.value.id, feedbackValue);
    
    // 设置反馈状态
    hasFeedback.value = true;
    feedbackType.value = type;
    
    const feedbackText = type === 'helpful' ? '有帮助' : '无帮助';
    message.success(`感谢您的反馈！您标记此内容为${feedbackText}。`);
  } catch (error) {
    console.error('提交反馈失败:', error);
    message.error('提交反馈失败，请稍后重试');
  }
};

// 处理联系支持
const handleContactSupport = () => {
  message.info('正在跳转到支持联系表单...');
  
  // TODO: 实现联系支持功能
  console.log('Contact support for question:', questionData.value?.id);
};
</script>

<style scoped lang="less">
.question-detail {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.detail-header {
  margin-bottom: 24px;
}

.detail-content {
  .prose {
    line-height: 1.6;
    
    :deep(p) {
      margin-bottom: 16px;
    }
    
    :deep(ul), :deep(ol) {
      margin: 16px 0;
      padding-left: 24px;
    }
    
    :deep(li) {
      margin-bottom: 8px;
    }
    
    :deep(strong) {
      font-weight: 600;
    }
    
    :deep(em) {
      font-style: italic;
    }
  }
}

.detail-actions {
  .ant-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }
  
  .ant-btn:disabled {
    cursor: not-allowed;
  }
}

.feedback-result {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
