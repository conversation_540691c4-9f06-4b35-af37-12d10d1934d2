<!--
  个人信息弹窗组件
  功能：显示和编辑用户个人信息
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="Personal Information"
    :width="500"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="personal-info-modal">
      <div class="form-item">
        <label>Profile Photo:</label>
        <div class="profile-photo">
          <div class="avatar-display">
            <UserOutlined />
          </div>
        </div>
      </div>

      <div class="form-item">
        <label>Nickname:</label>
        <div class="value">{{ formData.nickname }}</div>
      </div>

      <div class="form-item">
        <label>Time Zone:</label>
        <div class="value">{{ formData.timezone }}</div>
      </div>

      <div class="modal-actions">
        <a-button @click="handleCancel">cancel</a-button>
        <a-button type="primary" @click="handleEdit">edit</a-button>
      </div>
    </div>
  </a-modal>

  <!-- 编辑弹窗 -->
  <a-modal
    v-model:open="editModalVisible"
    title="Edit Personal Information"
    :width="500"
    @ok="handleSave"
    @cancel="handleCancelEdit"
  >
    <div class="edit-form">
      <div class="form-item">
        <label>Profile Photo:</label>
        <div class="profile-photo">
          <div class="avatar-display">
            <UserOutlined />
          </div>
          <a-button size="small" style="margin-left: 12px;">Change Photo</a-button>
        </div>
      </div>

      <div class="form-item">
        <label>Nickname:</label>
        <a-input v-model:value="editFormData.nickname" placeholder="Enter nickname" />
      </div>

      <div class="form-item">
        <label>Time Zone:</label>
        <a-select v-model:value="editFormData.timezone" style="width: 100%">
          <a-select-option value="Shanghai">Shanghai</a-select-option>
          <a-select-option value="Beijing">Beijing</a-select-option>
          <a-select-option value="New York">New York</a-select-option>
          <a-select-option value="London">London</a-select-option>
          <a-select-option value="Tokyo">Tokyo</a-select-option>
        </a-select>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { UserOutlined } from '@ant-design/icons-vue';

// Props 定义
interface Props {
  visible: boolean;
  userInfo: {
    nickname: string;
    role: string;
    timezone: string;
    avatar?: string;
  };
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'update': [userInfo: any];
}>();

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const editModalVisible = ref(false);

const formData = ref({
  nickname: '',
  timezone: '',
  role: ''
});

const editFormData = ref({
  nickname: '',
  timezone: ''
});

// 监听 props 变化
watch(() => props.userInfo, (newUserInfo) => {
  if (newUserInfo) {
    formData.value = { ...newUserInfo };
    editFormData.value = {
      nickname: newUserInfo.nickname,
      timezone: newUserInfo.timezone
    };
  }
}, { immediate: true });

// 方法定义
const handleCancel = () => {
  modalVisible.value = false;
};

const handleEdit = () => {
  editModalVisible.value = true;
};

const handleCancelEdit = () => {
  editModalVisible.value = false;
  // 重置编辑表单
  editFormData.value = {
    nickname: formData.value.nickname,
    timezone: formData.value.timezone
  };
};

const handleSave = () => {
  // 更新数据
  formData.value.nickname = editFormData.value.nickname;
  formData.value.timezone = editFormData.value.timezone;
  
  // 发送更新事件
  emit('update', {
    nickname: editFormData.value.nickname,
    timezone: editFormData.value.timezone
  });
  
  editModalVisible.value = false;
};
</script>

<style scoped lang="less">
// .personal-info-modal {
//   padding: 20px;
// }

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  label {
    width: 120px;
    font-weight: 500;
    color: #333;
  }

  .value {
    flex: 1;
    color: #666;
  }
}

.profile-photo {
  display: flex;
  align-items: center;
}

.avatar-display {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.edit-form {
  padding: 20px 0;

  .form-item {
    flex-direction: column;
    align-items: flex-start;

    label {
      width: auto;
      margin-bottom: 8px;
    }
  }
}
</style>
