<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="1000" @ok="handleSubmit">
    <div class="suspicious-record-detail">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="串货记录ID">
          {{ recordData.suspiciousId || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备UUID">
          {{ recordData.deviceUuid || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备绑定供应商">
          {{ recordData.providerName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="供应商销售地区">
          {{ recordData.providerLocation || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="用户账号">
          {{ recordData.userAccount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="用户昵称">
          {{ recordData.userNick || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备激活IP">
          {{ recordData.activeIp || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="IP所属区域">
          {{ recordData.activeLocation || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="核实状态">
          <a-tag :color="getStatusColor(recordData.status)">
            {{ getStatusText(recordData.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="核实人名称">
          {{ recordData.verifiUserName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="核实时间">
          {{ recordData.verifiTime || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="核实备注" :span="2">
          {{ recordData.remark || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getDictItemsByCode } from '/@/utils/dict/index';

  // Emits声明
  const emit = defineEmits(['register', 'success']);
  
  const recordData = ref<any>({});
  
  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    
    if (data?.record) {
      // 使用列表返回的串货记录数据
      recordData.value = data.record;
    }
  });

  // 设置标题
  const title = computed(() => '串货记录详情');

  // 获取状态颜色
  function getStatusColor(status: string) {
    switch (status) {
      case '1':
        return 'default';
      case '2':
        return 'red';
      case '3':
        return 'green';
      default:
        return 'orange';
    }
  }

  // 获取状态文本 - 使用字典
  function getStatusText(status: string) {
    const arr = getDictItemsByCode('verifi_status') || [];
    const found = arr.find(item => item.value == status);
    return found ? found.text : '未知';
  }

  // 表单提交事件
  async function handleSubmit() {
    closeModal();
    emit('success');
  }
</script>

<style lang="less" scoped>
  .suspicious-record-detail {
    padding: 16px;
    
    :deep(.ant-descriptions-item-label) {
      font-weight: 500;
      width: 120px;
    }
  }
</style> 