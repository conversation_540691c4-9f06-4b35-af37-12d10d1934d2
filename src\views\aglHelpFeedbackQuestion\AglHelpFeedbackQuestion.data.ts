import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

//列表数据
export const columns: BasicColumn[] = [
   {
    title: t('airgle.helpFeedback.userName'),
    align:"center",
    sorter: true,
    dataIndex: 'userName'
   },
   {
    title: t('airgle.helpFeedback.feedbackCategory'),
    align:"center",
    sorter: true,
    dataIndex: 'feedbackCategory_dictText'
   },
   {
    title: t('airgle.helpFeedback.feedbackContent'),
    align:"center",
    sorter: true,
    dataIndex: 'feedbackContent'
   },
   {
    title: t('airgle.helpFeedback.questionTime'),
    align:"center",
    sorter: true,
    dataIndex: 'questionTime'
   },
   {
    title: t('airgle.helpFeedback.userPhone'),
    align:"center",
    sorter: true,
    dataIndex: 'userPhone'
   },
   {
    title: t('airgle.helpFeedback.userEmail'),
    align:"center",
    sorter: true,
    dataIndex: 'userEmail'
   },
   {
    title: t('airgle.helpFeedback.questionFrequency'),
    align:"center",
    sorter: true,
    dataIndex: 'questionFrequency_dictText'
   },
   {
    title: t('airgle.helpFeedback.isAgree'),
    align:"center",
    sorter: true,
    dataIndex: 'isAgree_dictText'
   },
   {
    title: t('airgle.helpFeedback.attach'),
    align:"center",
    sorter: true,
    dataIndex: 'attach'
   },
   {
    title: t('airgle.helpFeedback.handleStatus'),
    align:"center",
    sorter: true,
    dataIndex: 'handleStatus_dictText'
   },
   {
    title: t('airgle.helpFeedback.handleRecord'),
    align:"center",
    sorter: true,
    dataIndex: 'handleRecord'
   },
   {
    title: t('airgle.helpFeedback.handleUser'),
    align:"center",
    sorter: true,
    dataIndex: 'handleUser'
   },
   {
    title: t('airgle.helpFeedback.handleTime'),
    align:"center",
    sorter: true,
    dataIndex: 'handleTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: t('airgle.helpFeedback.userName'),
      field: "userName",
      component: 'Input',
      //colProps: {span: 6},
	},
	{
      label: t('airgle.helpFeedback.feedbackCategory'),
      field: 'feedbackCategory',
      component: 'JTreeSelect',
      componentProps:{
          dict:"agl_help_type,type_name,id",
          pidValue:"0",
      },
      //colProps: {span: 6},
 	},
	{
      label: t('airgle.helpFeedback.questionTime'),
      field: 'questionTime',
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      //colProps: {span: 6},
 	},
	{
      label: t('airgle.helpFeedback.userPhone'),
      field: "userPhone",
      component: 'Input',
      //colProps: {span: 6},
	},
	{
      label: t('airgle.helpFeedback.userEmail'),
      field: "userEmail",
      component: 'Input',
      //colProps: {span: 6},
	},
	{
      label: t('airgle.helpFeedback.questionFrequency'),
      field: 'questionFrequency',
      component: 'Select',
      componentProps: {
        dictCode: 'agl_question_frequency',
      },
      //colProps: {span: 6},
 	},
	{
      label: t('airgle.helpFeedback.isAgree'),
      field: 'isAgree',
      component: 'Select',
      componentProps: {
        dictCode: 'yn',
      },
      //colProps: {span: 6},
 	},
	{
      label: t('airgle.helpFeedback.handleStatus'),
      field: 'handleStatus',
      component: 'Select',
      componentProps: {
        dictCode: 'agl_question_status',
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: t('airgle.helpFeedback.userName'),
    field: 'userName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterUserName')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.feedbackCategory'),
    field: 'feedbackCategory',
    component: 'JTreeSelect',
    componentProps:{
        dict:"agl_help_type,type_name,id",
        pidValue:"0",
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseSelectFeedbackCategory')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.feedbackContent'),
    field: 'feedbackContent',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterFeedbackContent')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.questionTime'),
    field: 'questionTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterQuestionTime')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.userPhone'),
    field: 'userPhone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterUserPhone')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.userEmail'),
    field: 'userEmail',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterUserEmail')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.questionFrequency'),
    field: 'questionFrequency',
    component: 'Select',
    componentProps: {
      dictCode: 'agl_question_frequency',
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseSelectQuestionFrequency')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.isAgree'),
    field: 'isAgree',
    component: 'Select',
    componentProps: {
      dictCode: 'yn',
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseSelectIsAgree')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.attach'),
    field: 'attach',
    component: 'JUpload',
  },
  {
    label: t('airgle.helpFeedback.handleStatus'),
    field: 'handleStatus',
    component: 'Select',
    componentProps: {
      dictCode: 'agl_question_status',
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseSelectHandleStatus')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.handleRecord'),
    field: 'handleRecord',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterHandleRecord')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.handleUser'),
    field: 'handleUser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterHandleUser')},
          ];
     },
  },
  {
    label: t('airgle.helpFeedback.handleTime'),
    field: 'handleTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpFeedback.pleaseEnterHandleTime')},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  userName: {title: t('airgle.helpFeedback.userName'),order: 0,view: 'text', type: 'string',},
  feedbackCategory: {title: t('airgle.helpFeedback.feedbackCategory'),order: 1,view: 'sel_tree', type: 'string',dict: 'agl_help_type,type_name,id', pidValue: '0',},
  feedbackContent: {title: t('airgle.helpFeedback.feedbackContent'),order: 2,view: 'text', type: 'string',},
  questionTime: {title: t('airgle.helpFeedback.questionTime'),order: 3,view: 'datetime', type: 'string',},
  userPhone: {title: t('airgle.helpFeedback.userPhone'),order: 4,view: 'text', type: 'string',},
  userEmail: {title: t('airgle.helpFeedback.userEmail'),order: 5,view: 'text', type: 'string',},
  questionFrequency: {title: t('airgle.helpFeedback.questionFrequency'),order: 6,view: 'sel', type: 'string',dict: 'agl_question_frequency',},
  isAgree: {title: t('airgle.helpFeedback.isAgree'),order: 7,view: 'sel', type: 'string',dict: 'yn',},
  attach: {title: t('airgle.helpFeedback.attach'),order: 8,view: 'text', type: 'string',},
  handleStatus: {title: t('airgle.helpFeedback.handleStatus'),order: 9,view: 'sel', type: 'string',dict: 'agl_question_status',},
  handleRecord: {title: t('airgle.helpFeedback.handleRecord'),order: 10,view: 'text', type: 'string',},
  handleUser: {title: t('airgle.helpFeedback.handleUser'),order: 11,view: 'text', type: 'string',},
  handleTime: {title: t('airgle.helpFeedback.handleTime'),order: 12,view: 'datetime', type: 'string',},
};
