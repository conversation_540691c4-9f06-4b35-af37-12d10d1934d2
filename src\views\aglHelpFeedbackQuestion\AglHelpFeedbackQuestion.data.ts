import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '反馈用户',
    align: "center",
    dataIndex: 'userName'
  },
  {
    title: '问题分类',
    align: "center",
    sorter: true,
    dataIndex: 'feedbackCategory',
    customRender:({text}) => {
       return render.renderCategoryTree(text,'C04');
   },
  },
  {
    title: '问题发生时间',
    align: "center",
    dataIndex: 'questionTime'
  },
  {
    title: '用户手机',
    align: "center",
    sorter: true,
    dataIndex: 'userPhone'
  },
  {
    title: '用户邮箱',
    align: "center",
    dataIndex: 'userEmail'
  },
  {
    title: '问题频率',
    align: "center",
    sorter: true,
    dataIndex: 'questionFrequency_dictText'
  },
  {
    title: '同意授权',
    align: "center",
    sorter: true,
    dataIndex: 'isAgree_dictText'
  },
  {
    title: '处理状态',
    align: "center",
    sorter: true,
    dataIndex: 'handleStatus_dictText'
  },
  {
    title: '处理记录',
    align: "center",
    dataIndex: 'handleRecord'
  },
  {
    title: '处理人',
    align: "center",
    dataIndex: 'handleUser'
  },
  {
    title: '处理时间',
    align: "center",
    dataIndex: 'handleTime'
  },
];

// 高级查询数据
export const superQuerySchema = {
  userName: {title: '反馈用户',order: 0,view: 'text', type: 'string',},
  feedbackCategory: {title: '问题分类',order: 1,view: 'cat_tree', type: 'string',pcode: 'C04',},
  questionTime: {title: '问题发生时间',order: 3,view: 'datetime', type: 'string',},
  userPhone: {title: '用户手机',order: 4,view: 'text', type: 'string',},
  userEmail: {title: '用户邮箱',order: 5,view: 'text', type: 'string',},
  questionFrequency: {title: '问题频率',order: 6,view: 'list', type: 'string',dictCode: 'agl_question_frequency',},
  isAgree: {title: '同意授权',order: 7,view: 'radio', type: 'string',dictCode: 'yn',},
  handleStatus: {title: '处理状态',order: 9,view: 'list', type: 'string',dictCode: 'agl_question_status',},
  handleRecord: {title: '处理记录',order: 10,view: 'text', type: 'string',},
  handleUser: {title: '处理人',order: 11,view: 'text', type: 'string',},
  handleTime: {title: '处理时间',order: 12,view: 'datetime', type: 'string',},
};
