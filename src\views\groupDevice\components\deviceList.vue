<!--
  群组设备列表组件
  功能：展示群组内设备列表，仅用于显示和选择查看图表，无控制功能
  作者：开发团队
  创建时间：2024
-->
<template>
  <!-- 主容器卡片 -->
  <a-card style="width: 100%">
    <!-- 设备列表表格 -->
    <a-table 
      :columns="columns" 
      :data-source="devices" 
      row-key="id" 
      :pagination="false"
      :scroll="{ x: 300, y: 750 }" 
      :loading="loading"
      :row-selection="null"
    >
      <!-- 单元格自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <!-- 设备名称列自定义渲染 -->
        <template v-if="column.dataIndex === 'name'">
          <div class="flex items-center gap-2 device-row"
            :class="{ 'device-row-selected': selectedDeviceId === record.id }" 
            @click="handleDeviceClick(record)"
            @dblclick="handleDeviceDoubleClick(record)">
            <!-- 设备图标 -->
            <img class="rounded-full-img" alt="" width="32" :src="`https://images.tuyacn.com/${record.icon}`" />
            <div>
              <!-- 设备名称（双击可查看详情） -->
              <div class="device-name">{{ record.name }}</div>
              <!-- 产品名称 -->
              <div class="text-gray-400 text-xs">{{ record.productName }}</div>
              <!-- 位置名称 -->
              <div class="text-gray-400 text-xs">{{ record.locationName }}</div>
            </div>
          </div>
        </template>
        
        <!-- 状态列自定义渲染 -->
        <template v-else-if="column.dataIndex === 'status'">
          <div class="status-indicator">
            <div class="status-dot" :class="getStatusClass(record)"></div>
            <span>{{ record.powerOn ? 'Power On' : 'Power Off' }}</span>
          </div>
        </template>
        
        <!-- 操作列自定义渲染 -->
        <template v-else-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button 
              type="text" 
              size="small" 
              danger
              @click="handleRemoveDevice(record)"
              class="remove-btn"
              title="移除设备"
            >
              <Icon icon="ant-design:minus-circle-filled" color="#ff4d4f" :size="18" />
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 分页控制区域 -->
    <div class="selectPage display-between">
      <!-- 每页显示数量选择 -->
      <div class="flex-align">
        Show：
        <a-select v-model:value="pageSize" style="width: 60px" @change="handlePageSizeChange">
          <a-select-option :value="20">20</a-select-option>
          <a-select-option :value="50">50</a-select-option>
        </a-select>
      </div>
      <!-- 跳转到指定页面 -->
      <div class="flex-align"> Jump to page： <a-input v-model:value="pageInput" style="width: 80px" placeholder="Enter"
          @pressEnter="handleJumpToPage" /> </div>
    </div>

    <!-- 分页信息和分页器 -->
    <div class="display-between">
      <div></div>
      <div class="flex-align">
        <!-- 分页信息显示 -->
        {{ `${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, total)}` }} of {{ total }} items
        <!-- 分页组件 -->
        <Pagination 
          style="margin-left: 15px" 
          v-model:current="currentPage" 
          :page-size="pageSize" 
          :total="total"
          :show-size-changer="false"
          @change="onPageChange" 
          show-less-items 
        />
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits, defineExpose, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Pagination } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { getCurrentWeather, getDeviceSignal } from '../../deviceManagement/api/deviceManagement.api';
import { getDevicesByGroupId, removeDevicesFromGroup } from '../api/groupManagement.api';
import { Icon } from '/@/components/Icon';
/**
 * 设备数据接口定义
 */
interface Device {
  id: string;           // 设备ID
  name: string;         // 设备名称
  locationName: string; // 位置名称
  productName: string;  // 产品名称
  icon: string;         // 设备图标
  status: string;       // 设备状态
  lat: string;          // 设备纬度
  lon: string;          // 设备经度
  powerOn?: boolean;    // 电源状态（可选）
  speed?: string;       // 风速设置（可选）
  lock?: boolean;       // 童锁状态（可选）
  uv?: boolean;         // UV状态（可选）
  activeTime?: string;  // 活跃时间（可选）
  category?: string;    // 设备类别（可选）
  createTime?: string;  // 创建时间（可选）
  updateTime?: string;  // 更新时间（可选）
  environment?: string; // 环境（室内/室外）
  isOnline?: boolean;   // 在线状态
  dataPublication?: boolean; // 数据发布状态
  mode?: string;        // 设备模式
}

/**
 * 信号数据接口定义
 */
interface SignalData {
  signalLevel: string;
  indicatorType: string;
  signal: number;
  eventTime: number;
}

// 定义props
interface Props {
  groupId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  groupId: ''
});

// 路由实例
const router = useRouter();

// 定义事件
const emit = defineEmits(['deviceSelected']);

// ===== 响应式数据定义 =====
const devices = ref<Device[]>([]);        // 设备列表数据
const loading = ref(false);               // 加载状态
const pageInput = ref<number>(1);         // 跳转页码输入
const selectedDeviceId = ref<string>(''); // 当前选中的设备ID
const currentPage = ref(1);               // 当前页码
const pageSize = ref(20);                 // 每页显示数量
const total = ref(0);                     // 总数据量

/**
 * 表格列配置
 */
const columns = [
  { 
    title: 'Devices', 
    dataIndex: 'name',
    key: 'name', 
    width: 180
  },
  { 
    title: 'Status', 
    dataIndex: 'status',
    key: 'status', 
    width: 120
  },
  {
    dataIndex: 'action',
    key: 'action',
    width: 50,
    fixed: 'right'
  }
];

/**
 * 获取设备列表数据
 */
const fetchDeviceList = async () => {
  console.log('fetchDeviceList called, groupId:', props.groupId);
  
  // 如果没有群组ID，不执行查询
  if (!props.groupId) {
    console.log('No groupId provided, clearing devices');
    devices.value = [];
    total.value = 0;
    return;
  }

  try {
    loading.value = true;
    console.log('Calling API with groupId:', props.groupId);
    
    // 调用群组设备查询API
    const response = await getDevicesByGroupId(props.groupId);
    console.log('API response:', response);
    
    // API直接返回设备数组，而不是包含result的对象
    const deviceArray = Array.isArray(response) ? response : [];
    console.log('Device array:', deviceArray);
    
    if (deviceArray.length > 0) {
      // 处理设备状态
      const processedDevices = deviceArray.map(device => {
        let powerOn = false;
        let speed = '1';
        let lock = false;
        let uv = false;

        if (device.status) {
          try {
            const statusArray = JSON.parse(device.status);
            statusArray.forEach((item: { code: string; value: any }) => {
              switch (item.code) {
                case 'switch':
                  powerOn = item.value;
                  break;
                case 'speed':
                  speed = item.value;
                  break;
                case 'lock':
                  lock = item.value;
                  break;
                case 'uv':
                  uv = item.value;
                  break;
              }
            });
          } catch (error) {
            console.error('解析设备状态失败:', error);
          }
        }

        return {
          ...device,
          powerOn,
          speed,
          lock,
          uv,
          isOnline: String(device.isOnline).toLowerCase() === 'true',
          locationName: device.locationName || '',
          key: device.id
        };
      });

      console.log('Processed devices:', processedDevices);
      console.log('Setting devices.value to:', processedDevices);
      devices.value = processedDevices;
      total.value = processedDevices.length; // 直接使用数组长度
      currentPage.value = 1;
      pageSize.value = 20;
      console.log('Final devices.value:', devices.value);
      console.log('Final total.value:', total.value);
    } else {
      console.log('No devices found in response');
      devices.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取群组设备列表错误:', error);
    message.error('获取群组设备列表失败');
  } finally {
    loading.value = false;
  }
};

// 监听群组ID变化，自动刷新设备列表
watch(() => props.groupId, (newGroupId) => {
  if (newGroupId) {
    currentPage.value = 1;
    fetchDeviceList();
  } else {
    devices.value = [];
    total.value = 0;
  }
}, { immediate: true });

// 获取状态样式类
const getStatusClass = (device: Device) => {
  return {
    'status-on': device.powerOn,
    'status-off': !device.powerOn
  };
};

/**
 * 页码变化处理
 * @param page 新的页码
 */
const onPageChange = (page: number) => {
  currentPage.value = page;
  fetchDeviceList();
};

/**
 * 每页显示数量变化处理
 * 重置到第一页并重新获取数据
 */
const handlePageSizeChange = () => {
  currentPage.value = 1;
  fetchDeviceList();
};

/**
 * 跳转到指定页面
 * 验证输入的页码是否有效
 */
const handleJumpToPage = () => {
  if (pageInput.value >= 1 && pageInput.value <= Math.ceil(total.value / pageSize.value)) {
    currentPage.value = pageInput.value;
    fetchDeviceList();
  } else {
    message.warning('请输入有效的页码');
  }
};

/**
 * 选中当前设备，请求设备数据和天气数据
 * @param device 设备对象
 */
const handleDeviceClick = async (device: Device) => {
  console.log('选中设备:', device);

  // 设置选中的设备ID
  selectedDeviceId.value = device.id;

  try {
    // 获取天气数据
    const weatherResult = await getCurrentWeather(device.lat, device.lon);

    // 尝试获取信号数据，如果设备不支持则使用默认值
    let signalData: SignalData = {
      signalLevel: 'Unknown',
      indicatorType: 'RSSI',
      signal: 0,
      eventTime: Date.now()
    };

    try {
      const signalResult = await getDeviceSignal(device.id, device.category || 'WiFi');
      if (signalResult?.result) {
        signalData = signalResult.result;
      }
    } catch (signalError) {
      console.log('设备信号获取失败，可能不支持此功能:', signalError);
    }

    // 处理天气数据
    let weatherData: any = {};

    // 处理天气数据 - API返回的数据直接就是result内容
    if (weatherResult && weatherResult.current_weather) {
      const currentWeather = weatherResult.current_weather;
      weatherData = {
        temperature: currentWeather.temp,
        humidity: currentWeather.humidity,
        condition: currentWeather.condition,
        realFeel: currentWeather.real_feel,
        windSpeed: currentWeather.wind_speed,
        pressure: currentWeather.pressure,
        uvi: currentWeather.uvi
      };
    }

    // 处理设备数据
    const deviceData = {
      id: device.id,
      name: device.name,
      locationName: device.locationName,
      productName: device.productName,
      // 添加空气质量数据
      aqi: weatherResult?.air_quality?.aqi,
      pm25: weatherResult?.air_quality?.pm25,
      temperature: weatherData.temperature,
      humidity: weatherData.humidity,
      // 添加信号数据
      signalLevel: signalData.signalLevel,
      indicatorType: signalData.indicatorType,
      signalStrength: signalData.signal,
      signalEventTime: signalData.eventTime
    };

    // 发出设备选中事件，同时传递设备数据和天气数据
    emit('deviceSelected', deviceData, weatherData);
  } catch (error: any) {
    console.log('error--------', error);
    // 只有当天气数据获取失败时才显示错误提示
    if (error.message !== 'not support this device') {
      message.error('获取数据失败');
    }
  }
};

/**
 * 跳转到设备详情页面
 * @param device 设备对象
 */
const handleDeviceDoubleClick = (device: Device) => {
  router.push({
    path: '/deviceManagement/detail',
    query: {
      id: device.id,
      deviceType: 'WiFi',
      icon: device.icon // 传递设备图标信息
    }
  });
};

/**
 * 移除设备
 * @param device 设备对象
 */
const handleRemoveDevice = async (device: Device) => {
  console.log('移除设备:', device);
  try {
    await removeDevicesFromGroup(props.groupId || '', [device.id]);
    message.success('设备移除成功');
    fetchDeviceList(); // 刷新列表
  } catch (error) {
    console.error('移除设备失败:', error);
    message.error('移除设备失败');
  }
};

// 暴露方法给父组件
defineExpose({
  fetchDeviceList,
});

// 生命周期
onMounted(() => {
  console.log('Device list component mounted, fetching device list...');
  fetchDeviceList();
});
</script>

<style scoped lang="less">
// 设备行样式
.device-row {
  padding: 8px 0;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.device-row-selected {
    background-color: #f5f9fe;
    border-left: 3px solid #1890ff;
  }
}

.device-name {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
}

.rounded-full-img {
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.status-on {
    background-color: #52c41a;
  }

  &.status-off {
    background-color: #ff4d4f;
  }
}

.selectPage {
  margin-top: 16px;
  padding: 0 8px;
}

.display-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 0 8px;
}

.flex-align {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.remove-btn {
  color: #ff4d4f;
  font-size: 16px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  
  &:hover {
    color: #ff7875;
    background-color: #fff2f0;
  }
}
</style>
