<template>
    <div class="dashboard-container">
        <div class="workspace-dropdown-list">
            <a-select
                v-model:value="selectedWorkspace"
                style="width: 200px"
                :placeholder="t('airgle.locationOverview.workspace.placeholder')"
                @change="handleWorkspaceChange"
            >
                <a-select-option v-for="workspace in workspaces" :key="workspace.id" :value="workspace.id">
                    {{ workspace.workspaceName }}
                </a-select-option>
            </a-select>
        </div>

        <!-- Background Map -->
        <div id="map" class="background-map"></div>

        <!-- Left Sidebar -->
        <div class="left-sidebar">
            <div class="sidebar-content">
                <div style="padding: 20px 0 50px 0;">
                    <a-switch v-model:checked="isActive" :style="{ transform: 'rotate(90deg)' }" />
                </div>

                <!-- Location Tabs -->
                <div class="location-tabs">
                    <div class="tab-item" :class="{ active: activeTab === 'outdoor' }" @click="activeTab = 'outdoor'">
                        <EnvironmentOutlined />
                        <span>{{ t('airgle.locationOverview.tabs.outdoor') }}</span>
                    </div>
                    <div class="tab-item" :class="{ active: activeTab === 'indoor' }" @click="activeTab = 'indoor'">
                        <HomeOutlined />
                        <span>{{ t('airgle.locationOverview.tabs.indoor') }}</span>
                    </div>
                </div>

                <!-- Metrics -->
                <div class="metrics-list">
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.aqi') }} :</div>
                        <div class="metric-value">{{ outdoorData.aqi }}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.pm25') }} :</div>
                        <div class="metric-value">{{ outdoorData.pm25 }}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.co2') }} :</div>
                        <div class="metric-value">{{ outdoorData.co2 }}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.o3') }} :</div>
                        <div class="metric-value">{{ outdoorData.o3 }}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.temperature') }} :</div>
                        <div class="metric-value">{{ displayTemperature }}<span class="unit temperature-unit" @click="toggleTemperatureUnit">°{{ temperatureUnit }}</span></div>

                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.humidity') }} :</div>
                        <div class="metric-value">{{ outdoorData.humidity }}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">{{ t('airgle.locationOverview.metrics.pressure') }} :</div>
                        <div class="metric-value">{{ outdoorData.pressure }}</div>
                    </div>
                </div>

                <!-- Top Button -->
                <div class="top-button">
                    <span>{{ t('airgle.locationOverview.map.controls.top') }}</span>
                    <CaretUpOutlined />
                </div>
            </div>
        </div>

        <!-- Map Area -->
        <div class="map-area">
            <!-- Map Controls -->
            <div class="map-controls">
                <div class="custom-controls">
                    <div class="zoom-controls">
                        <button class="control-btn" @click="zoomIn">
                            <PlusOutlined />
                        </button>
                        <button class="control-btn" @click="zoomOut">
                            <MinusOutlined />
                        </button>
                    </div>
                    <div class="location-info">
                        <span>{{ currentLocation }}</span>
                    </div>
                </div>
            </div>

            <!-- Air Quality Legend -->
            <div class="air-quality-legend">
                <div class="legend-item good">{{ t('airgle.locationOverview.map.legend.good') }}</div>
                <div class="legend-item moderate">{{ t('airgle.locationOverview.map.legend.moderate') }}</div>
                <div class="legend-item unhealthy-sensitive">{{ t('airgle.locationOverview.map.legend.unhealthySensitive') }}</div>
                <div class="legend-item unhealthy">{{ t('airgle.locationOverview.map.legend.unhealthy') }}</div>
                <div class="legend-item very-unhealthy">{{ t('airgle.locationOverview.map.legend.veryUnhealthy') }}</div>
                <div class="legend-item hazardous">{{ t('airgle.locationOverview.map.legend.hazardous') }}</div>
            </div>
        </div>

        <!-- Right Panels -->
        <div class="right-panels">
            <!-- Weather Panel -->
            <div class="weather-panel panel" :class="{ collapsed: weatherCollapsed }">
                <button class="collapse-btn" @click="weatherCollapsed = !weatherCollapsed">
                    <LeftOutlined v-if="weatherCollapsed" />
                    <RightOutlined v-else />
                </button>

                <div class="panel-content" v-if="!weatherCollapsed">
                    <!-- Time Section -->
                    <div class="time-section">
                        <div class="time-header">
                            <div class="time-label">
                                <span>{{ t('airgle.locationOverview.weather.localTime') }}</span>
                                <span class="location"> {{currentLocation}}
                                    <ReloadOutlined />
                                </span>
                            </div>
                            <div class="weather-icon">
                                <img class="dashboard-panel-sun-img" src="/src/assets/weather/cloudy.png" />
                            </div>
                        </div>
                        <div class="time-display">
                            <div class="current-time">{{ currentTime }}</div>
                            <div class="current-date">{{ currentDate }}</div>
                        </div>
                    </div>

                    <!-- Outdoor Tag -->
                    <div class="outdoor-tag">
                        <span>{{ t('airgle.locationOverview.weather.outdoor') }}</span>
                    </div>

                    <!-- AQI Display -->
                    <div class="aqi-display">
                        <div class="aqi-main">
                            <span class="aqi-number" :style="{ color: getAQIColor(outdoorData.aqi) }">{{ outdoorData.aqi }}</span>
                            <span class="aqi-label">{{ t('airgle.locationOverview.weather.aqi') }}</span>
                        </div>
                        <div class="quality-status">
                            <AirQualityIndicator :value="outdoorData.aqi" type="outdoor" :description="getAQIDescription(outdoorData.aqi)" />
                        </div>
                    </div>

                    <!-- Metrics Grid -->
                    <div class="weather-metrics">
                        <div class="weather-metric">
                            <div class="metric-label">{{ t('airgle.locationOverview.weather.metrics.temp') }}</div>
                            <div class="metric-value">
                                {{ displayTemperature }}
                                <span class="unit temperature-unit" @click="toggleTemperatureUnit">
                                    °{{ temperatureUnit }}
                                </span>
                            </div>
                        </div>
                        <div class="weather-metric">
                            <div class="metric-label">{{ t('airgle.locationOverview.weather.metrics.humidity') }}</div>
                            <div class="metric-value">{{ outdoorData.humidity }}<span class="unit">{{ t('airgle.locationOverview.weather.units.percent') }}</span></div>
                        </div>
                        <div class="weather-metric">
                            <div class="metric-label">{{ t('airgle.locationOverview.weather.metrics.pm25') }}</div>
                            <div class="metric-value">{{ outdoorData.pm25 }}<span class="unit">{{ t('airgle.locationOverview.weather.units.ugm3') }}</span></div>
                        </div>
                        <div class="weather-metric">
                            <div class="metric-label">{{ t('airgle.locationOverview.weather.metrics.co2') }}</div>
                            <div class="metric-value">{{ outdoorData.co2 }}<span class="unit">{{ t('airgle.locationOverview.weather.units.ppm') }}</span></div>
                        </div>
                    </div>
                </div>

                <div class="collapsed-content" v-if="weatherCollapsed">
                    <div class="collapsed-text">{{ t('airgle.locationOverview.weather.title') }}</div>
                </div>
            </div>

            <!-- Indoor Panel -->
            <div class="indoor-panel panel" :class="{ collapsed: indoorCollapsed }">
                <button class="collapse-btn" @click="indoorCollapsed = !indoorCollapsed">
                    <LeftOutlined v-if="indoorCollapsed" />
                    <RightOutlined v-else />
                </button>

                <div class="panel-content" v-if="!indoorCollapsed">

                    <!-- Room Selector -->
                    <div class="room-selector">
                        <select v-model="selectedRoomIndex">
                            <option v-for="(room, index) in indoorData" :key="index" :value="index">
                                {{ room.roomName }}
                            </option>
                        </select>
                    </div>

                    <!-- Device Info -->
                    <div class="device-info">
                        <div class="info-row">
                            <span class="info-label">{{ t('airgle.locationOverview.indoor.deviceInfo.device') }}:</span>
                            <span class="info-value">{{ deviceNames }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">{{ t('airgle.locationOverview.indoor.deviceInfo.totalDevices') }}:</span>
                            <span class="info-value">{{ currentRoomDevices.length }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">{{ t('airgle.locationOverview.indoor.deviceInfo.connection') }}:</span>
                            <span class="info-value">{{ deviceConnectionStatus }}</span>
                        </div>
                    </div>

                    <!-- Indoor Tag -->
                    <div class="indoor-tag">
                        <span>{{ t('airgle.locationOverview.indoor.title') }}</span>
                    </div>

                    <!-- Indoor AQI -->
                    <div class="indoor-aqi">
                        <div class="aqi-value-display">
                            <span class="aqi-big-number" :style="{ color: getIndoorAirQualityColor(currentRoom?.roomAirQuality || 0) }">{{ currentRoom?.roomAirQuality || 0 }}</span>
                        </div>
                        <AirQualityIndicator :value="currentRoom?.roomAirQuality || 0" type="indoor" :description="getIndoorAirQualityDescription(currentRoom?.roomAirQuality || 0)" />
                    </div>
                </div>

                <div class="collapsed-content" v-if="indoorCollapsed">
                    <div class="collapsed-text">{{ t('airgle.locationOverview.indoor.title') }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineProps, computed } from 'vue'
import {
    EnvironmentOutlined,
    HomeOutlined,
    CaretUpOutlined,
    ReloadOutlined,
    LeftOutlined,
    RightOutlined,
    PlusOutlined,
    MinusOutlined
} from '@ant-design/icons-vue'
import AirQualityIndicator from './components/air-quality-indicator.vue'
import { loadGoogleMaps } from '@/utils/googleMaps'
import {
    getWorkspaceList,
    getRoomAndDeviceByWorkspaceId,
    getCurrentWeather,
    type WorkspaceRecord,
    type RoomInfo
} from './api/locationOverview.api'
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n()
// 声明 google 类型
declare const google: any

// 接口定义已移至 ./api/locationOverview.api.ts

// Define props for map center coordinates
const props = defineProps({
    lat: {
        type: Number,
        default: 27.6648 // Default Florida latitude
    },
    lon: {
        type: Number,
        default: -81.5158 // Default Florida longitude
    }
})

// Reactive data
const activeTab = ref('outdoor')
const selectedRoomIndex = ref<number>(0)
const currentTime = ref('')
const currentDate = ref('')
const weatherCollapsed = ref(false)
const indoorCollapsed = ref(false)
const isActive = ref(true)
const currentLocation = ref('Florida')
// 添加温度单位切换状态
const temperatureUnit = ref<'C' | 'F'>('C')
let map: any = null


// Workspace data
const workspaces = ref<WorkspaceRecord[]>([])
const selectedWorkspace = ref<string | undefined>(undefined)

// 室内数据
const indoorData = ref<RoomInfo[]>([])

// Sample data
const outdoorData = ref({
    aqi: 10,
    pm25: 10,
    co2: 10,
    o3: 20,
    temp: 30,
    humidity: 22,
    pressure: 22
})
// 添加温度转换函数
const convertTemperature = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
    if (fromUnit === toUnit) return temp

    if (fromUnit === 'C' && toUnit === 'F') {
        return Math.round((temp * 9/5) + 32)
    } else if (fromUnit === 'F' && toUnit === 'C') {
        return Math.round((temp - 32) * 5/9)
    }

    return temp
}

// 添加显示温度的计算属性
const displayTemperature = computed(() => {
    // 后端传来的是摄氏度，如果当前单位是华氏度就转换
    if (temperatureUnit.value === 'F') {
        return convertTemperature(outdoorData.value.temp, 'C', 'F')
    }
    return outdoorData.value.temp
})

// 添加温度单位切换函数
const toggleTemperatureUnit = () => {
    temperatureUnit.value = temperatureUnit.value === 'C' ? 'F' : 'C'
}
// AQI 颜色和描述函数
const getAQIColor = (aqi: number): string => {
    if (aqi <= 50) return '#4CAF50'      // 绿色 - 好的
    if (aqi <= 100) return '#FFEB3B'     // 黄色 - 缓和
    if (aqi <= 150) return '#FF9800'     // 橙色 - 对敏感人群不健康
    if (aqi <= 200) return '#F44336'     // 红色 - 不良
    if (aqi <= 300) return '#9C27B0'     // 紫色 - 非常不健康
    return '#795548'                     // 栗色 - 危险
}

const getAQIDescription = (aqi: number): string => {
    if (aqi <= 50) return t('airgle.locationOverview.airQuality.good')
    if (aqi <= 100) return t('airgle.locationOverview.airQuality.moderate')
    if (aqi <= 150) return t('airgle.locationOverview.airQuality.unhealthySensitive')
    if (aqi <= 200) return t('airgle.locationOverview.airQuality.unhealthy')
    if (aqi <= 300) return t('airgle.locationOverview.airQuality.veryUnhealthy')
    return t('airgle.locationOverview.airQuality.hazardous')
}

// 室内空气质量枚举转换函数（1-6转换为颜色和描述）
const getIndoorAirQualityColor = (quality: number): string => {
    switch (quality) {
        case 6: return '#4CAF50'  // 最好 - 绿色
        case 5: return '#8BC34A'  // 很好 - 浅绿色
        case 4: return '#FFEB3B'  // 好 - 黄色
        case 3: return '#FF9800'  // 一般 - 橙色
        case 2: return '#F44336'  // 差 - 红色
        case 1: return '#795548'  // 很差 - 栗色
        default: return '#9E9E9E' // 无数据 - 灰色
    }
}

const getIndoorAirQualityDescription = (quality: number): string => {
    switch (quality) {
        case 6: return t('airgle.locationOverview.indoor.airQuality.excellent')
        case 5: return t('airgle.locationOverview.indoor.airQuality.veryGood')
        case 4: return t('airgle.locationOverview.indoor.airQuality.good')
        case 3: return t('airgle.locationOverview.indoor.airQuality.fair')
        case 2: return t('airgle.locationOverview.indoor.airQuality.poor')
        case 1: return t('airgle.locationOverview.indoor.airQuality.veryPoor')
        default: return t('airgle.locationOverview.indoor.airQuality.noData')
    }
}

// 计算属性：当前选中的房间
const currentRoom = computed(() => {
    return indoorData.value[selectedRoomIndex.value] || null
})

// 计算属性：当前房间的设备列表
const currentRoomDevices = computed(() => {
    return currentRoom.value?.devices || []
})

// 计算属性：设备连接状态
const deviceConnectionStatus = computed(() => {
    if (!currentRoomDevices.value.length) return t('airgle.locationOverview.indoor.status.noDevices')

    const onlineDevices = currentRoomDevices.value.filter(device => device.isOnline === 'true')
    const offlineDevices = currentRoomDevices.value.filter(device => device.isOnline === 'false')

    if (offlineDevices.length === 0) {
        return t('airgle.locationOverview.indoor.status.allOnline')
    } else if (onlineDevices.length === 0) {
        return t('airgle.locationOverview.indoor.status.allOffline')
    } else {
        return t('airgle.locationOverview.indoor.status.mixed', {
            online: onlineDevices.length,
            offline: offlineDevices.length
        })
    }
})

// 计算属性：设备名称列表
const deviceNames = computed(() => {
    return currentRoomDevices.value.map(device => device.name).join(', ') || t('airgle.locationOverview.indoor.status.noDevices')
})

// Load workspaces
const loadWorkspaces = async () => {
    try {
        const response = await getWorkspaceList({})
        if (response && response.result && response.result.records) {
            workspaces.value = response.result.records
        }
    } catch (error) {
        console.error('Failed to load workspaces:', error)
    }
}

// Handle workspace selection
const handleWorkspaceChange = async (workspaceId: string) => {
    try {
        // 加载室内数据
        const indoorResponse = await getRoomAndDeviceByWorkspaceId(workspaceId)


        // 尝试不同的数据访问方式
        let roomData: RoomInfo[] | null = null
        if (indoorResponse) {
            if (Array.isArray(indoorResponse)) {
                // 如果响应直接是数组
                roomData = indoorResponse as RoomInfo[]
            } else if (indoorResponse.result && Array.isArray(indoorResponse.result)) {
                // 如果数据在result字段中
                roomData = indoorResponse.result
            } else if (indoorResponse.data && Array.isArray(indoorResponse.data)) {
                // 如果数据在data字段中
                roomData = indoorResponse.data
            } else {
                console.log('No array data found, complete response:', indoorResponse)
            }
        }

        if (roomData && roomData.length > 0) {
            indoorData.value = roomData
            selectedRoomIndex.value = 0 // 重置为第一个房间

        } else {
            console.error('No valid room data found')
        }

        // 获取选中的工作空间信息
        const workspace = workspaces.value.find(ws => ws.id === workspaceId)
        if (workspace) {
            // 调用天气API获取数据
            try {
                const weatherResponse = await getCurrentWeather(
                    workspace.latitude.toString(),
                    workspace.longitude.toString()
                )

                console.log('Weather API response:', weatherResponse)

                // 更新 outdoorData 为天气API返回的数据
                // 支持两种响应格式：直接在根级别或在result字段中
                const airQuality = weatherResponse.air_quality || weatherResponse.result?.air_quality
                const currentWeather = weatherResponse.current_weather || weatherResponse.result?.current_weather

                if (airQuality && currentWeather) {
                    outdoorData.value = {
                        aqi: parseInt(airQuality.aqi) || 0,
                        pm25: parseInt(airQuality.pm25) || 0,
                        co2: parseFloat(airQuality.co) || 0,
                        o3: parseInt(airQuality.o3) || 0,
                        temp: parseInt(currentWeather.temp) || 0,
                        humidity: parseInt(currentWeather.humidity) || 0,
                        pressure: parseInt(currentWeather.pressure) || 0
                    }

                    // 移动地图到工作空间位置
                    if (map) {
                        const newPosition = { lat: workspace.latitude, lng: workspace.longitude }
                        map.setCenter(newPosition)
                        map.setZoom(15)

                        // 清除之前的标记
                        // 添加新的标记
                        new google.maps.Marker({
                            position: newPosition,
                            map: map,
                            title: workspace.workspaceName,
                            icon: {
                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="20" cy="20" r="18" fill="${getAQIColor(outdoorData.value.aqi)}" stroke="white" stroke-width="2"/>
                                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="12" font-weight="bold">${outdoorData.value.aqi}</text>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(40, 40),
                                anchor: new google.maps.Point(20, 20)
                            }
                        })

                        // 更新当前位置显示
                        const geocoder = new google.maps.Geocoder()
                        geocoder.geocode({ location: newPosition }, (results, status) => {
                            if (status === 'OK' && results?.[0]) {
                                currentLocation.value = results[0].formatted_address
                            } else {
                                currentLocation.value = workspace.workspaceLocation || `${workspace.latitude.toFixed(4)}, ${workspace.longitude.toFixed(4)}`
                            }
                        })
                    }
                } else {
                    console.error('Weather data structure not as expected:', weatherResponse)
                }
            } catch (weatherError) {
                console.error('Failed to load weather data:', weatherError)
            }
        }
    } catch (error) {
        console.error('Failed to load room and device data:', error)
    }
}

// Map control functions
const zoomIn = () => {
    if (map) {
        const currentZoom = map.getZoom()
        map.setZoom(currentZoom + 1)
    }
}

const zoomOut = () => {
    if (map) {
        const currentZoom = map.getZoom()
        map.setZoom(currentZoom - 1)
    }
}

// Initialize Google Maps
const initMap = async () => {
    try {
        await loadGoogleMaps()
        const mapElement = document.getElementById('map')
        if (!mapElement) return

        // 默认位置（佛罗里达）
        const defaultPosition = {
            lat: props.lat,
            lng: props.lon
        }

        // 尝试获取当前位置
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    createMapWithPosition(mapElement, {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    })
                },
                () => {
                    createMapWithPosition(mapElement, defaultPosition)
                }
            )
        } else {
            createMapWithPosition(mapElement, defaultPosition)
        }
    } catch (error) {
        console.error('Failed to initialize Google Maps:', error)
    }
}

// 创建地图的辅助函数
const createMapWithPosition = (mapElement: HTMLElement, position: { lat: number, lng: number }) => {
    // 最基础的地图配置
    const google = (window as any).google;
    const mapOptions = {
        center: position,
        zoom: 15,
        mapTypeId: google.maps.MapTypeId.SATELLITE,
        disableDefaultUI: true
    };

    // 创建地图实例
    map = new google.maps.Map(mapElement, mapOptions);

    // 添加当前位置标记
    new google.maps.Marker({
        position: position,
        map: map
    });

    // 反向地理编码
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ location: position }, (results, status) => {
        if (status === 'OK' && results?.[0]) {
            currentLocation.value = results[0].formatted_address;
        } else {
            currentLocation.value = `${position.lat.toFixed(4)}, ${position.lng.toFixed(4)}`;
        }
    });
}

// Time update function
const updateTime = () => {
    const now = new Date()
    currentTime.value = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
    currentDate.value = now.toLocaleDateString('en-CA')
}

let timeInterval: NodeJS.Timeout

onMounted(() => {
    console.log('=== LocationOverview Component Mounted ===')
    updateTime()
    timeInterval = setInterval(updateTime, 1000)
    initMap()
    loadWorkspaces() // Load workspaces when component mounts
})

onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval)
    }
})
</script>

<style scoped>
.dashboard-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.workspace-dropdown-list {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.background-map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Left Sidebar */
.left-sidebar {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 120px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 60px;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.sidebar-content {
    padding: 20px 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.location-tabs {
    margin-bottom: 30px;
    width: 100%;
}

.tab-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 0;
    margin-bottom: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.tab-item.active {
    background: rgba(74, 144, 226, 0.1);
    color: #4A90E2;
}

.tab-item span {
    margin-left: 8px;
}

.metrics-list {
    flex: 1;
    width: 100%;
}

.metric-row {
    margin-bottom: 15px;
    font-size: 16px;
}

.metric-row .metric-label {
    color: #000;
}

.metric-value {
    color: #4A90E2;
    font-weight: 600;
}

.top-button {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #4A90E2;
    font-size: 14px;
    cursor: pointer;
    margin-top: auto;
}

/* Map Area */
.map-area {
    position: absolute;
    left: 160px;
    right: 360px;
    top: 20px;
    bottom: 20px;
    z-index: 5;
    pointer-events: none;
}

.map-controls {
    position: absolute;
    bottom: 80px;
    transform: translateX(-100%);
    z-index: 100;
    pointer-events: auto;
}

.custom-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.zoom-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 8px;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #666;
    transition: all 0.2s;
}

.control-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.location-info {
    background: white;
    padding: 8px 16px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    font-size: 14px;
    color: #333;
    text-align: center;
}

.air-quality-legend {
    position: absolute;
    bottom: 20px;
    left: 0;
    transform: translateX(-20%);
    display: flex;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    overflow: hidden;
}

.legend-item {
    padding: 8px 16px;
    color: white;
    white-space: nowrap;
}

.legend-item.good {
    background: #4CAF50;
}

.legend-item.moderate {
    background: #FFEB3B;
    color: #333;
}

.legend-item.unhealthy-sensitive {
    background: #FF9800;
}

.legend-item.unhealthy {
    background: #F44336;
}

.legend-item.very-unhealthy {
    background: #9C27B0;
}

.legend-item.hazardous {
    background: #795548;
}

/* Right Panels */
.right-panels {
    position: absolute;
    right: 20px;
    top: 20px;
    bottom: 20px;
    width: 450px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    z-index: 10;
}

.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    position: relative;
    transition: all 0.3s ease;
}

.panel.collapsed {
    width: 50px;
    transform: translateX(400px);
    height: 360px;
    /* 确保有足够高度显示按钮 */
}

.collapse-btn {
    position: absolute;
    top: 50%;
    left: -30px;
    transform: translateY(-50%);
    width: 30px;
    height: 60px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px 0 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.panel-content {
    padding: 20px;
}

.collapsed-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    opacity: 1;
    visibility: visible;
}

.panel.collapsed .panel-content {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    left: -9999px;
}

.panel.collapsed .collapsed-content {
    opacity: 1;
    visibility: visible;
    position: static;
}

.collapsed-icon {
    font-size: 20px;
    color: #666;
}

/* Weather Panel */
.weather-panel {
    /* flex: 1; */
}

.time-section {
    margin-bottom: 20px;
}
/* 温度单位切换按钮样式 */
.temperature-unit {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    padding: 2px 4px;
    margin-left: 2px;
}

.temperature-unit:hover {
    background-color: #e3f2fd;
    color: #1976d2;
    transform: scale(1.1);
}

.temperature-unit:active {
    background-color: #bbdefb;
    transform: scale(0.95);
}
.time-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.time-label {
    display: flex;
    flex-direction: column;
    color: #666;
}

.location {
    color: #333;
    margin-top: 2px;
}

.weather-icon {
    width: 60px;
    font-size: 32px;
    color: #FFA726;
}

.time-display {
    text-align: left;
}

.current-time {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

.current-date {
    color: #666;
    margin-top: 2px;
}

.outdoor-tag {
    display: inline-block;
    background: #4A90E2;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    margin-bottom: 15px;
}

.aqi-display {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.aqi-main {
    margin-bottom: 10px;
}

.aqi-number {
    font-size: 30px;
    font-weight: bold;
    color: #9C27B0;
}

.aqi-label {
    color: #666;
    margin-left: 8px;
}

.quality-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quality-bar {
    width: 120px;
    height: 8px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.quality-gradient {
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #4CAF50, #FFEB3B, #FF9800, #F44336, #9C27B0);
}

.quality-pointer {
    position: absolute;
    right: 20px;
    top: -2px;
    width: 12px;
    height: 12px;
    background: #9C27B0;
    border-radius: 50%;
}

.quality-text {
    color: #9C27B0;
    font-weight: 500;
}

.weather-metrics {
    display: flex;
    justify-content: space-evenly;
    gap: 15px;
}

.weather-metric {
    text-align: center;
}

.weather-metric .metric-label {
    font-size: 18px;
    color: #525252;
    margin-bottom: 5px;
}

.weather-metric .metric-value {
    font-size: 30px;
    color: #4A90E2;
}

.unit {
    font-size: 14px;
    color: #666;
}

/* Indoor Panel */
.indoor-panel {
    /* flex: 1; */
}

.room-selector {
    position: relative;
    margin-bottom: 20px;
}

.room-selector select {
    padding: 8px 12px;
    font-size: 14px;
}

.device-info {
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
}

.indoor-tag {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #4CAF50;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    margin-bottom: 15px;
}

.indoor-aqi{
    display: flex;
    justify-content: space-around;
}

.aqi-value-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 8px;
}

.aqi-big-number {
    font-size: 32px;
    font-weight: bold;
    color: #4CAF50;
}

.aqi-description {
    color: #666;
}

.collapsed-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.collapsed-text {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    color: #666;
    font-weight: 500;
}

/* 移除之前的地图控件样式 */
:deep(.gm-control-active),
:deep(.gm-bundled-control),
:deep(.gm-style-mtc) {
    display: none !important;
}
</style>
