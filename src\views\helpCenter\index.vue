<template>
    <div class="help-center">
        <!-- FAQ List View -->
        <div v-if="!selectedQuestion">
            <h1 class="text-2xl font-bold mb-6">Help Center</h1>

            <!-- Search and Feedback Section -->
            <div class="flex items-center gap-4 mb-6">
                <div class="flex-1 relative">
                    <input
                      v-model="searchQuery"
                      type="text"
                      class="w-full px-4 py-2 border rounded-lg"
                      placeholder="Enter your questions"
                      @keyup.enter="handleSearch"
                    />
                    <Button type="primary" class="absolute right-1 top-1 bottom-1" @click="handleSearch">
                        <SearchOutlined />
                    </Button>
                </div>
                <Button class="flex items-center gap-2" @click="handleFeedbackClick">
                    <MessageOutlined />
                    My feedback
                </Button>
            </div>

            <!-- Navigation Tabs -->
            <div class="flex justify-between items-center mb-6">
                <div class="text-lg">FAQ</div>
                <div class="text-primary cursor-pointer">My Device FAQS</div>
            </div>

            <div style="background: #fff;padding: 30px 50px;border-radius: 10px;">
                <!-- Category Tabs -->
                <div class="flex gap-8 mb-6 border-b">
                    <div
                      v-for="category in categories"
                      :key="category.id"
                      :class="[
                        'pb-2 cursor-pointer',
                        activeTab === category.id ? 'text-primary border-b-2 border-primary' : ''
                      ]"
                      @click="handleTabChange(category.id)"
                    >
                        {{ category.typeName }}
                    </div>
                </div>

                <!-- FAQ List -->
                <div class="faq-list">
                    <!-- Loading State -->
                    <div v-if="loading" class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <div class="mt-2 text-gray-500">Loading...</div>
                    </div>

                    <!-- FAQ Items -->
                    <div v-else-if="filteredQuestions.length > 0">
                        <div v-for="(question, index) in filteredQuestions" :key="question.id || index"
                            class="py-4 border-b flex justify-between items-center cursor-pointer hover:bg-gray-50"
                            @click="handleQuestionClick(question)">
                            <div class="flex-1">{{ question.typeName }}</div>
                            <RightOutlined />
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else class="text-center py-8">
                        <div class="text-gray-400 text-lg mb-2">No questions found</div>
                        <div class="text-gray-500">Try adjusting your search or category filter</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Detail View -->
        <QuestionDetail
          v-else
          :questionId="selectedQuestion"
          @back="selectedQuestion = null"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Button, message } from 'ant-design-vue';
import { SearchOutlined, MessageOutlined, RightOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import {
  getHelpCategories,
  getHelpInfoList,
  type HelpCategory,
  type HelpInfo,
} from './api';
import QuestionDetail from './components/QuestionDetail.vue';

const router = useRouter();

// 显示用的问题接口
interface DisplayQuestion {
  id: string;
  title: string;
  content: string;
  category: string;
  typeName?: string;
}

const searchQuery = ref('');
const activeTab = ref('1'); // 默认选择第一个分类
const loading = ref(false);
const categories = ref<HelpCategory[]>([]);
const questions = ref<HelpInfo[]>([]);
const selectedQuestion = ref<string | null>(null);

// 加载帮助分类
const loadCategories = async () => {
  try {
    const response = await getHelpCategories();
    console.log(response)
    const allCategories = response.records || [];
    // 只显示父级分类（pid为"0"的分类）
    categories.value = allCategories.filter(cat => cat.pid === '0');

    // 设置默认选中第一个分类
    if (categories.value.length > 0) {
      activeTab.value = categories.value[0].id;
    }
  } catch (error) {
    console.error('Error fetching categories:', error);
    message.error('Failed to load categories');
  }
};

// 加载帮助信息列表
const loadHelpInfoList = async (pid: string, typeName?: string) => {
  try {
    const response = await getHelpInfoList(pid, typeName);
    questions.value = response.records || [];
  } catch (error) {
    console.error('Error fetching help info:', error);
    message.error('Failed to load help information');
  }
};

// 计算过滤后的问题列表
const filteredQuestions = computed<DisplayQuestion[]>(() => {
  return questions.value.map(item => ({
    id: item.id,
    title: item.helpTitle,
    content: item.helpContent,
    category: item.helpType,
    typeName: item.typeName
  }));
});

// 处理分类切换
const handleTabChange = (categoryId: string) => {
  activeTab.value = categoryId;
};

// 处理搜索
const handleSearch = () => {
  loadHelpInfoList(activeTab.value, searchQuery.value);
};

// 处理问题点击
const handleQuestionClick = (question: DisplayQuestion) => {
  console.log('Question clicked:', question);
  selectedQuestion.value = question.id;
};

// 监听分类变化  
watch(() => activeTab.value, (newPid) => {
  if (newPid) {
    loadHelpInfoList(newPid, searchQuery.value);
  }
});

// Handle feedback button click
const handleFeedbackClick = () => {
  router.push('/online/cgformList');
};

// 初始化
onMounted(() => {
  loadCategories();
});
</script>

<style lang="less" scoped>
.help-center {
    padding: 30px 60px;
}

.text-primary {
    color: #1890ff;
}

.border-primary {
    border-color: #1890ff;
}
</style>
