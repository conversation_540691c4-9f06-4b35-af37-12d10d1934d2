<!--
  设备分组列表组件
  功能：展示设备分组列表，支持分页、批量删除、分组电源控制等操作
  作者：开发团队
  创建时间：2024
-->
<template>
  <div class="group-list-container">
    <!-- 主容器卡片 -->
    <a-card style="width: 100%">
      <!-- 分组列表表格 -->
      <a-table
        :columns="columns"
        :data-source="groups"
        row-key="id"
        :pagination="false"
        :row-selection="rowSelection"
        :loading="loading"
      >
        <!-- 表头自定义渲染 -->
        <template #headerCell="{ column }">
          <template v-if="column.key === 'power'">
            <span class="flex-align">
              {{ column.title }}
              <!-- 批量删除按钮 -->
              <div class="delete-icn" @click="handleDelete"
                   :disabled="deleteLoading">
                <DeleteOutlined style="color: red; font-size: 20px;" />
              </div>
            </span>
          </template>
        </template>

        <!-- 单元格自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 分组名称列自定义渲染 -->
          <template v-if="column.dataIndex === 'name'">
            <div class="flex items-center gap-2 group-row"
              :class="{ 'group-row-selected': selectedGroupId === record.id }"
              @click="handleGroupClick(record)"
              @dblclick="handleGroupDoubleClick(record)">
              <!-- 分组图标 -->
              <img class="rounded-full-img" alt="" width="32" :src="getGroupIcon(record)" />
              <div>
                <!-- 分组名称（双击可查看详情） -->
                <div class="group-name">{{ record.name }}</div>
                <!-- 设备数量 -->
                <div class="text-gray-400 text-xs">{{ record.deviceCount }} devices</div>
                <!-- 位置信息 -->
                <div class="text-gray-400 text-xs">{{ record.location }}</div>
              </div>
            </div>
          </template>

          <!-- 电源控制列自定义渲染 -->
          <template v-else-if="column.dataIndex === 'power'">
            <div class="controlPower">
              <!-- 控制图标 -->
              <div @click="handleControl(record)" class="control-div display-center">
                <img src="@/assets/device/control.png" class="control-img" alt="" />
              </div>
              <!-- 电源开关按钮 -->
              <Button shape="circle" style="width: 38px;height: 38px;flex-shrink: 0;"
                :class="record.powerOn ? 'power-btn' : ''"
                @click="togglePower(record)">
                <template #icon>
                  <PoweroffOutlined />
                </template>
              </Button>
            </div>
          </template>
        </template>
      </a-table>

      <!-- 分页控制区域 -->
      <div class="selectPage display-between">
        <!-- 每页显示数量选择 -->
        <div class="flex-align">
          Show：
          <a-select v-model:value="pageSize" style="width: 60px" @change="handlePageSizeChange">
            <a-select-option :value="20">20</a-select-option>
            <a-select-option :value="50">50</a-select-option>
          </a-select>
        </div>
        <!-- 跳转到指定页面 -->
        <div class="flex-align"> Jump to page： <a-input v-model:value="pageInput" style="width: 80px" placeholder="Enter"
            @pressEnter="handleJumpToPage" /> </div>
      </div>

      <!-- 分页信息和分页器 -->
      <div class="display-between">
        <div></div>
        <div class="flex-align">
          <!-- 分页信息显示 -->
          {{ `${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, total)}` }} of {{ total }} items
          <!-- 分页组件 -->
          <Pagination
            style="margin-left: 15px"
            v-model:current="currentPage"
            :page-size="pageSize"
            :total="total"
            :show-size-changer="false"
            @change="onPageChange"
            show-less-items
          />
        </div>
      </div>
    </a-card>

    <!-- 分组控制弹窗 -->
    <a-modal v-model:open="controlModalVisible" :title="null" :footer="null" :closable="false" width="400px" centered
      :bodyStyle="{ padding: 0 }" wrapClassName="device-control-modal">
      <div class="control-modal-container">
        <!-- 头部区域 -->
        <div class="control-header">
          <div class="header-title">{{ currentControlGroup?.name || 'Group' }}</div>
          <div class="header-right" @click="closeControlModal">
            <CloseOutlined style="color: white; font-size: 18px;" />
          </div>
        </div>

        <!-- 控制内容区域 -->
        <div class="control-content">
          <!-- 中心电源按钮 -->
          <div class="power-section">
            <div class="power-button" :class="{ 'power-on': currentControlGroup?.powerOn }" @click="toggleGroupPower">
              <PoweroffOutlined style="font-size: 48px;" />
            </div>
          </div>

          <!-- 控制选项列表 -->
          <div class="control-options">
            <!-- 童锁 选项 -->
            <div class="control-option">
              <span class="option-label">Lock</span>
              <a-switch v-model:checked="isLockOn" @change="handleLockChange" />
            </div>

            <!-- UV 选项 -->
            <div class="control-option">
              <span class="option-label">UV</span>
              <a-switch v-model:checked="titaniumProUV" @change="handleTitaniumUVChange" />
            </div>

            <!-- Speed 选项 -->
            <div class="control-option">
              <span class="option-label">Speed</span>
              <a-select v-model:value="selectedWindSpeed" style="width: 80px" @change="handleWindSpeedChange"
                size="small">
                <a-select-option v-for="item in windSpeedOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  DeleteOutlined,
  PoweroffOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import { Pagination, Button } from 'ant-design-vue';
import { getGroupList, type GroupRecord, type GroupListParams, type GroupStatusItem, sendGroupCommand, type GroupCommandParams, deleteGroupsBatch } from '../api/groupManagement.api';

// 类型定义 - 扩展API返回的GroupRecord
interface GroupData extends GroupRecord {
  name: string;         // 使用groupName作为name
  deviceCount?: number; // 设备数量（可选，需要额外计算）
  location: string;     // 使用workspaceName作为location
  status: string;       // 状态
  devices?: string[];   // 设备列表（可选）
  powerOn?: boolean;    // 电源状态（可选）
  speed?: string;       // 风速设置（可选）
  lock?: boolean;       // 童锁状态（可选）
  uv?: boolean;         // UV状态（可选）
  isOnline?: boolean;   // 在线状态（可选）
  environment?: string; // 环境类型（可选）
  dataPublication?: boolean; // 数据发布状态（可选）
  mode?: string;        // 设备模式（可选）
}

// 响应式数据
const loading = ref(false);
const groups = ref<GroupData[]>([]);
const selectedGroupId = ref<string>('');
const selectedRowKeys = ref<string[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const pageInput = ref(1);

const deleteLoading = ref(false);

// 过滤参数
const filters = ref<Record<string, any>>({
  connection: null,       // 连接状态
  environment: null,      // 环境类型
  data_publication: null, // 数据发布状态
  mode: null,             // 设备模式
});
const searchKeyword = ref('');

// 控制弹窗相关
const controlModalVisible = ref(false);
const currentControlGroup = ref<GroupData | null>(null);
const titaniumProUV = ref(false);
const selectedWindSpeed = ref('1');
const isLockOn = ref(false);

// 事件定义
const emit = defineEmits(['groupSelected', 'showGroupDevices', 'showGroupDetail']);

// 表格列配置
const columns = [
  {
    title: 'Groups',
    dataIndex: 'name',
    key: 'name',
    width: 210
  },
  {
    title: 'Power',
    dataIndex: 'power',
    key: 'power',
    width: 90
  }
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (newSelectedRowKeys: string[]) => {
    selectedRowKeys.value = newSelectedRowKeys;
  }
}));

// 方法定义
// 获取分组图标
const getGroupIcon = (group: GroupData) => {
  // 如果没有图标或图标URL无效，使用本地默认图片
  if (!group.iconUrl) {
    return '/src/assets/device/airgle.png';
  }

  // 如果是完整的URL，直接使用
  if (group.iconUrl.startsWith('http')) {
    return group.iconUrl;
  }

  // 如果是相对路径，使用本地默认图片
  return '/src/assets/device/airgle.png';
};

// 单击群组 - 选择群组并显示设备列表
const handleGroupClick = (group: GroupData) => {
  selectedGroupId.value = group.id;
  console.log('Selected group:', group);
  emit('groupSelected', group);
  emit('showGroupDevices', group);
};

// 双击群组 - 显示群组详情页面
const handleGroupDoubleClick = (group: GroupData) => {
  console.log('Double clicked group:', group);
  emit('showGroupDetail', group);
};

// 控制功能
const handleControl = (group: GroupData) => {
  currentControlGroup.value = group;
  controlModalVisible.value = true;
  
  // 直接使用群组数据中的状态信息初始化控制面板
  initializeControlPanelFromGroup(group);
};

// 从群组数据初始化控制面板
const initializeControlPanelFromGroup = (group: GroupData) => {
  console.log('从群组数据初始化控制面板:', group);
  
  // 初始化控制面板状态
  titaniumProUV.value = group.uv || false;
  selectedWindSpeed.value = group.speed || '1';
  isLockOn.value = group.lock || false;
  
  console.log('控制面板初始化完成:', {
    uv: titaniumProUV.value,
    speed: selectedWindSpeed.value,
    lock: isLockOn.value,
    powerOn: group.powerOn
  });
};

// 解析群组状态数据
const parseGroupStatus = (groupStatus: GroupStatusItem[] | undefined) => {
  const status = {
    powerOn: false,
    speed: '1',
    lock: false,
    uv: false,
    mode: '1'
  };

  if (groupStatus) {
    groupStatus.forEach((item: GroupStatusItem) => {
      switch (item.code) {
        case 'switch':
          status.powerOn = item.value === 'true';
          break;
        case 'speed':
          status.speed = item.value;
          break;
        case 'lock':
          status.lock = item.value === 'true';
          break;
        case 'uv':
          status.uv = item.value === 'true';
          break;
        case 'mode':
          status.mode = item.value;
          break;
      }
    });
  }

  return status;
};

// 刷新群组状态（在控制操作后调用）
const refreshGroupStatus = async (groupId: string) => {
  try {
    // 重新获取群组列表以更新状态
    await fetchGroupList();
    console.log('群组状态已刷新');
  } catch (error) {
    console.error('刷新群组状态失败:', error);
  }
};

// 通用设备控制方法
const sendDeviceCommand = async (groupId: string, params: GroupCommandParams, successMessage: string, errorMessage: string) => {
  try {
    console.log('发送设备控制命令:', { groupId, params });
    const response = await sendGroupCommand(groupId, params);
    console.log('设备控制响应:', response);
    console.log('响应类型:', typeof response);
    console.log('响应success字段:', response.success);
    console.log('响应success字段类型:', typeof response.success);
    console.log('响应完整结构:', JSON.stringify(response, null, 2));
    
    // 处理不同的响应格式
    let isSuccess = false;
    
    if (typeof response === 'boolean') {
      // 直接返回布尔值的情况
      isSuccess = response === true;
    } else if (response && typeof response.success === 'boolean') {
      // 包含success字段的对象
      isSuccess = response.success === true;
    } else if (response && response.code === 200) {
      // HTTP状态码200的情况
      isSuccess = true;
    }
    
    if (isSuccess) {
      console.log('控制成功，显示成功消息:', successMessage);
      message.success(successMessage);
      // 操作成功后刷新状态
      await refreshGroupStatus(groupId);
      return true;
    } else {
      console.log('控制失败，显示错误消息:', response.message || errorMessage);
      message.error(response.message || errorMessage);
      return false;
    }
  } catch (error) {
    console.error('设备控制异常:', error);
    message.error(errorMessage);
    return false;
  }
};

// 切换分组电源状态
const togglePower = async (group: GroupData) => {
  const newPowerState = !group.powerOn;
  const params: GroupCommandParams = {
    switch: newPowerState
  };
  
  const success = await sendDeviceCommand(
    group.id, 
    params, 
    `Group ${newPowerState ? 'turned on' : 'turned off'} successfully`,
    'Group power control failed'
  );
  
  if (success) {
    group.powerOn = newPowerState;
  }
};

// 切换分组电源状态（控制弹窗中）
const toggleGroupPower = async () => {
  if (currentControlGroup.value) {
    await togglePower(currentControlGroup.value);
  }
};

// 关闭控制弹窗
const closeControlModal = () => {
  controlModalVisible.value = false;
  currentControlGroup.value = null;
};

// 处理风速改变
const handleWindSpeedChange = async (value: string) => {
  if (currentControlGroup.value) {
    const params: GroupCommandParams = {
      speed: value
    };
    
    const success = await sendDeviceCommand(
      currentControlGroup.value.id,
      params,
      'Group speed changed successfully',
      'Group speed change failed'
    );
    
    if (success) {
      currentControlGroup.value.speed = value;
    }
  }
};

// 处理童锁开关
const handleLockChange = async (value: boolean) => {
  if (currentControlGroup.value) {
    const params: GroupCommandParams = {
      lock: value
    };
    
    const success = await sendDeviceCommand(
      currentControlGroup.value.id,
      params,
      `Group lock ${value ? 'enabled' : 'disabled'} successfully`,
      'Group lock setting failed'
    );
    
    if (success) {
      currentControlGroup.value.lock = value;
      isLockOn.value = value;
    }
  }
};

// 处理UV开关
const handleTitaniumUVChange = async (value: boolean) => {
  if (currentControlGroup.value) {
    const params: GroupCommandParams = {
      uv: value
    };
    
    const success = await sendDeviceCommand(
      currentControlGroup.value.id,
      params,
      `Group UV ${value ? 'enabled' : 'disabled'} successfully`,
      'Group UV setting failed'
    );
    
    if (success) {
      currentControlGroup.value.uv = value;
      titaniumProUV.value = value;
    }
  }
};

const handleDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('Please select groups to delete');
    return;
  }

  // 如果已经在删除中，直接返回
  if (deleteLoading.value) {
    return;
  }

  // 开始删除操作，设置加载状态
  deleteLoading.value = true;

  console.log('Delete groups:', selectedRowKeys.value);
  
  try {
    const ids = selectedRowKeys.value.join(',');
    console.log('批量删除群组参数:', { ids });
    
    const response = await deleteGroupsBatch(ids);
    console.log('批量删除响应:', response);
    
    message.success('群组删除成功！');
    selectedRowKeys.value = [];
    await fetchGroupList();
    
  } catch (error) {
    console.error('批量删除群组失败:', error);
    message.error('删除群组失败，请重试');
  } finally {
    // 删除操作完成，重置加载状态
    deleteLoading.value = false;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1;
  fetchGroupList();
};

const onPageChange = (page: number) => {
  currentPage.value = page;
  fetchGroupList();
};

const handleJumpToPage = () => {
  if (pageInput.value >= 1 && pageInput.value <= Math.ceil(total.value / pageSize.value)) {
    currentPage.value = pageInput.value;
    fetchGroupList();
  } else {
    message.warning('Please enter a valid page number');
  }
};

// 过滤相关方法
const applyFilters = (data: GroupData[]): GroupData[] => {
  return data.filter(group => {
    // 连接状态过滤
    if (filters.value.connection !== null && group.isOnline !== filters.value.connection) {
      return false;
    }

    // 环境过滤
    if (filters.value.environment !== null && group.environment !== filters.value.environment) {
      return false;
    }

    // 数据发布状态过滤
    if (filters.value.data_publication !== null && group.dataPublication !== filters.value.data_publication) {
      return false;
    }

    // 模式过滤
    if (filters.value.mode !== null && group.mode !== filters.value.mode) {
      return false;
    }

    return true;
  });
};

/**
 * 处理搜索
 * @param value 搜索关键词
 */
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  currentPage.value = 1; // 重置到第一页
  fetchGroupList();
};

/**
 * 处理在线状态变化
 * @param status 在线状态
 */
const handleOnlineStatusChange = (status: boolean | null) => {
  filters.value.connection = status;
  currentPage.value = 1; // 重置到第一页
  fetchGroupList();
};

/**
 * 处理环境过滤
 * @param value 环境值
 */
const handleEnvironmentChange = (value: string | null) => {
  filters.value.environment = value;
  currentPage.value = 1;
  fetchGroupList();
};

/**
 * 处理数据发布过滤
 * @param value 数据发布状态
 */
const handleDataPublicationChange = (value: boolean | null) => {
  filters.value.data_publication = value;
  currentPage.value = 1;
  fetchGroupList();
};

/**
 * 处理模式过滤
 * @param value 模式值
 */
const handleModeChange = (value: string | null) => {
  filters.value.mode = value;
  currentPage.value = 1;
  fetchGroupList();
};

const fetchGroupList = async () => {
  loading.value = true;
  try {
    const params: GroupListParams = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    };

    // 添加搜索关键词
    if (searchKeyword.value?.trim()) {
      params.groupName = searchKeyword.value.trim();
    }

    const response = await getGroupList(params);
    console.log('群组列表响应:', response);
    
    if (response?.records?.length) {
      let processedGroups: GroupData[] = response.records.map(record => {
        // 解析群组状态
        const status = parseGroupStatus(record.groupStatus);
        
        return {
          ...record,
          name: record.groupName || `${record.productId || 'Unnamed'} Group`,
          location: record.workspaceName_dictText || record.workspaceName || 'Unknown Location',
          status: 'Active',
          devices: [],
          // 使用解析后的状态数据
          powerOn: status.powerOn,
          speed: status.speed,
          lock: status.lock,
          uv: status.uv,
          mode: status.mode,
          // 添加模拟的过滤属性
          isOnline: Math.random() > 0.3, // 模拟在线状态
          environment: Math.random() > 0.5 ? 'indoor' : 'outdoor', // 模拟环境
          dataPublication: Math.random() > 0.5, // 模拟数据发布状态
        };
      });

      // 前端过滤
      processedGroups = applyFilters(processedGroups);

      groups.value = processedGroups;
      total.value = response.total || processedGroups.length;
      currentPage.value = response.current;
      pageSize.value = response.size;
      
      console.log('群组列表处理完成:', processedGroups);
    } else {
      message.error('获取分组列表失败');
    }
  } catch (error) {
    console.error('获取分组列表错误:', error);
    message.error('获取分组列表失败');
  } finally {
    loading.value = false;
  }
};

// 暴露方法
const refreshList = () => {
  fetchGroupList();
};

defineExpose({
  refreshList,
  handleSearch,
  handleOnlineStatusChange,
  handleEnvironmentChange,
  handleDataPublicationChange,
  handleModeChange
});

// 风速选项
const windSpeedOptions = [
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
];

// 生命周期
onMounted(() => {
  fetchGroupList();
});
</script>

<style scoped lang="less">
.group-list-container {
  width: 100%;
}

// 与deviceManagement leftList保持一致的样式
.group-row {
  padding: 8px 0;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.group-row-selected {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
  }
}

.group-name {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
}

.rounded-full-img {
  border-radius: 50%;
  object-fit: cover;
}

.controlPower {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-div {
  width: 38px;
  height: 38px;
  cursor: pointer;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.2s;

  &:hover {
    background: #e6f7ff;
  }
}

.control-img {
  width: 20px;
  height: 20px;
}

.power-btn {
  background: #52c41a !important;
  border-color: #52c41a !important;
  color: white !important;
}

.delete-icn {
  cursor: pointer;
  margin-left: 8px;

  &:hover {
    opacity: 0.7;
  }
}

.selectPage {
  margin-top: 16px;
  padding: 0 8px;
}

.display-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.display-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 0 8px;
}

.flex-align {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 控制弹窗样式
:deep(.device-control-modal .ant-modal-content) {
  border-radius: 20px;
  overflow: hidden;
}

.control-modal-container {
  width: 100%;
  background: #f5f5f5;
}

.control-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  cursor: pointer;
  min-width: 18px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.control-content {
  padding: 30px 20px;
  background: #f5f5f5;
  min-height: 400px;
}

.power-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.power-button {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
  border: 4px solid #d9d9d9;

  &:hover {
    background: #d9d9d9;
  }

  &.power-on {
    background: #52c41a;
    color: white;
    border-color: #52c41a;
    box-shadow: 0 0 20px rgba(82, 196, 26, 0.3);
  }
}

.control-options {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #fafafa;
  }
}

.option-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
</style>
