import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

//列表数据
export const columns: BasicColumn[] = [
   {
    title: t('airgle.helpType.typeName'),
    align: 'left',
    dataIndex: 'typeName'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: t('airgle.helpType.typeName'),
      field: "typeName",
      component: 'Input',
      //colProps: {span: 6},
     },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: t('airgle.helpType.parentNode'),
    field: 'pid',
    component: 'JTreeSelect',
    componentProps: {
      dict: "agl_help_type,type_name,id",
      pidField: "pid",
      pidValue: "0",
      hasChildField: "has_child",
    },
  },
  {
    label: t('airgle.helpType.typeName'),
    field: 'typeName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: t('airgle.helpType.pleaseEnterTypeName')},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  typeName: {title: t('airgle.helpType.typeName'),order: 1,view: 'text', type: 'string',},
};


/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
