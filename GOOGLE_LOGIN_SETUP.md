# Google登录功能配置指南

## 1. 前端配置已完成

Google登录功能已在前端实现，包括：

### 登录页面
- ✅ 主登录页面 (`src/views/sys/login/LoginForm.vue`)
- ✅ 迷你登录页面 (`src/views/system/loginmini/MiniLogin.vue`)
- ✅ 二维码登录页面 (`src/views/system/loginmini/MiniCodelogin.vue`)

### 用户设置页面
- ✅ 用户绑定设置 (`src/views/system/usersetting/WeChatDingSetting.vue`)

## 2. 后端配置

### 2.1 配置文件
在您的后端配置文件中添加Google OAuth2配置：

```yaml
type:
  GOOGLE:
    client-id: 5221044099-agphv1f27qp1nmull0q85c07rmqhbhlr.apps.googleusercontent.com
    client-secret: 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
    redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/google/callback
```

### 2.2 开发环境配置
如果使用localhost开发，可以配置为：

```yaml
type:
  GOOGLE:
    client-id: 5221044099-agphv1f27qp1nmull0q85c07rmqhbhlr.apps.googleusercontent.com
    client-secret: 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
    redirect-uri: http://localhost:8080/jeecg-boot/sys/thirdLogin/google/callback
```

## 3. Google Cloud Console配置

### 3.1 访问Google Cloud Console
1. 访问：https://console.cloud.google.com/
2. 创建新项目或选择现有项目

### 3.2 启用Google+ API
1. 在左侧菜单中选择"API和服务" > "库"
2. 搜索"Google+ API"并启用

### 3.3 创建OAuth 2.0凭据
1. 在左侧菜单中选择"API和服务" > "凭据"
2. 点击"创建凭据" > "OAuth 2.0客户端ID"
3. 选择应用类型：Web应用
4. 配置授权重定向URI：
   - 开发环境：`http://localhost:8080/jeecg-boot/sys/thirdLogin/google/callback`
   - 生产环境：`http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/google/callback`

### 3.4 获取凭据信息
创建完成后，您将获得：
- **客户端ID**：类似 `5221044099-agphv1f27qp1nmull0q85c07rmqhbhlr.apps.googleusercontent.com`
- **客户端密钥**：类似 `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`

## 4. 后端接口实现

确保后端实现了以下接口：

### 4.1 Google登录接口
```
GET /sys/thirdLogin/render/google
```
- 功能：渲染Google登录页面
- 参数：无
- 返回：Google OAuth2登录页面

### 4.2 Google回调接口
```
GET /sys/thirdLogin/google/callback
```
- 功能：处理Google OAuth2回调
- 参数：
  - `code`: 授权码
  - `state`: 状态参数
- 返回：登录token或用户信息

### 4.3 第三方登录接口
```
POST /sys/thirdLogin/login
```
- 功能：处理第三方登录
- 参数：
  - `token`: 第三方登录token
  - `thirdType`: 第三方类型（google）
- 返回：用户信息和系统token

## 5. 测试步骤

### 5.1 本地测试
1. 启动后端服务（端口8080）
2. 启动前端服务
3. 访问登录页面
4. 点击Google图标
5. 完成Google授权流程

### 5.2 生产环境测试
1. 确保域名配置正确
2. 在Google Cloud Console中添加生产域名
3. 更新配置文件中的redirect-uri
4. 测试完整的登录流程

## 6. 常见问题

### 6.1 重定向URI不匹配
- 确保Google Cloud Console中的重定向URI与配置文件中的完全一致
- 注意协议（http/https）和端口号

### 6.2 授权错误
- 检查client-id和client-secret是否正确
- 确保Google+ API已启用
- 检查应用是否在正确的Google Cloud项目中

### 6.3 跨域问题
- 确保前端和后端的域名配置正确
- 检查CORS配置

## 7. 安全注意事项

1. **保护客户端密钥**：不要在客户端代码中暴露client-secret
2. **HTTPS要求**：生产环境必须使用HTTPS
3. **域名验证**：确保重定向URI使用已验证的域名
4. **状态参数**：使用state参数防止CSRF攻击

## 8. 完成状态

- ✅ 前端Google登录按钮已添加
- ✅ 用户绑定功能已实现
- ⏳ 后端配置需要完成
- ⏳ Google Cloud Console配置需要完成
- ⏳ 接口测试需要完成

## 9. 下一步

1. 配置Google Cloud Console
2. 更新后端配置文件
3. 实现后端接口
4. 测试完整流程
5. 部署到生产环境 