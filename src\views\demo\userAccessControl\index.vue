<!--
  用户访问控制演示页面
  展示用户访问控制功能的完整实现
-->
<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>用户访问控制演示</h1>
      <p>这是一个完整的用户访问控制功能演示，包含个人信息管理、设备位置管理、群组管理等功能。</p>
    </div>

    <div class="demo-content">
      <!-- 功能说明 -->
      <a-card title="功能说明" style="margin-bottom: 24px;">
        <div class="feature-list">
          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>个人信息管理</h4>
              <p>用户可以查看和编辑个人信息，包括昵称、时区设置等</p>
            </div>
          </div>
          
          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>设备位置管理</h4>
              <p>管理设备位置信息，包括位置列表、编辑、删除等功能</p>
            </div>
          </div>
          
          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>添加设备位置</h4>
              <p>创建新的设备位置群组，配置地址和房间信息</p>
            </div>
          </div>
          
          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>加入群组</h4>
              <p>通过邀请码加入现有群组，扩展设备管理范围</p>
            </div>
          </div>
          
          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>消息中心</h4>
              <p>接收和管理系统消息，保持信息同步</p>
            </div>
          </div>

          <div class="feature-item">
            <CheckCircleOutlined class="feature-icon" />
            <div class="feature-text">
              <h4>设备位置列表</h4>
              <p>以列表形式展示所有设备位置，支持编辑和删除操作</p>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 操作按钮 -->
      <a-card title="快速操作" style="margin-bottom: 24px;">
        <div class="action-buttons">
          <a-button type="primary" @click="openUserAccessControl">
            <UserOutlined />
            打开用户访问控制页面
          </a-button>
          
          <a-button @click="showPersonalInfo">
            <EditOutlined />
            个人信息设置
          </a-button>
          
          <a-button @click="showDeviceLocation">
            <HomeOutlined />
            设备位置管理
          </a-button>
          
          <a-button @click="showAddLocation">
            <PlusOutlined />
            添加设备位置
          </a-button>
          
          <a-button @click="showJoinGroup">
            <TeamOutlined />
            加入群组
          </a-button>
        </div>
      </a-card>

      <!-- 技术特性 -->
      <a-card title="技术特性">
        <div class="tech-features">
          <a-tag color="blue">Vue 3 Composition API</a-tag>
          <a-tag color="green">TypeScript</a-tag>
          <a-tag color="orange">Ant Design Vue</a-tag>
          <a-tag color="purple">响应式设计</a-tag>
          <a-tag color="cyan">国际化支持</a-tag>
          <a-tag color="red">单元测试</a-tag>
        </div>
        
        <div class="tech-description">
          <p>本组件采用现代化的前端技术栈构建，具有以下特点：</p>
          <ul>
            <li>使用 Vue 3 Composition API 提供更好的逻辑复用和类型推导</li>
            <li>完整的 TypeScript 类型定义，确保代码质量和开发体验</li>
            <li>基于 Ant Design Vue 组件库，提供一致的用户界面</li>
            <li>响应式设计，支持桌面端和移动端设备</li>
            <li>支持国际化，可轻松扩展多语言</li>
            <li>包含完整的单元测试，保证代码质量</li>
          </ul>
        </div>
      </a-card>
    </div>

    <!-- 嵌入的用户访问控制组件 -->
    <PersonalInfoModal 
      v-model:visible="personalInfoVisible"
      :user-info="demoUserInfo"
      @update="handleUpdateUserInfo"
    />

    <DeviceLocationModal 
      v-model:visible="deviceLocationVisible"
      :location-data="demoLocationData"
      @update="handleUpdateLocation"
    />

    <AddLocationModal 
      v-model:visible="addLocationVisible"
      @add="handleAddLocation"
    />

    <JoinGroupModal 
      v-model:visible="joinGroupVisible"
      @join="handleJoinGroup"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  UserOutlined,
  EditOutlined,
  HomeOutlined,
  PlusOutlined,
  TeamOutlined
} from '@ant-design/icons-vue';

// 导入组件
import PersonalInfoModal from '/@/views/userAccessControl/components/PersonalInfoModal.vue';
import DeviceLocationModal from '/@/views/userAccessControl/components/DeviceLocationModal.vue';
import AddLocationModal from '/@/views/userAccessControl/components/AddLocationModal.vue';
import JoinGroupModal from '/@/views/userAccessControl/components/JoinGroupModal.vue';

// 导入类型
import type { UserInfo, LocationData } from '/@/views/userAccessControl/types';

const router = useRouter();

// 演示数据
const demoUserInfo = ref<UserInfo>({
  nickname: 'Demo User',
  role: 'Admin',
  timezone: 'Shanghai',
  email: '<EMAIL>'
});

const demoLocationData = ref<LocationData>({
  name: 'Demo Location',
  locationManagement: '5 Room(s)',
  location: 'Demo City',
  roomCount: 5,
  members: [
    {
      id: '1',
      name: 'Demo User',
      role: 'Admin'
    }
  ]
});

// 弹窗状态
const personalInfoVisible = ref(false);
const deviceLocationVisible = ref(false);
const addLocationVisible = ref(false);
const joinGroupVisible = ref(false);

// 方法
const openUserAccessControl = () => {
  router.push('/user-access-control');
};

const showPersonalInfo = () => {
  personalInfoVisible.value = true;
};

const showDeviceLocation = () => {
  deviceLocationVisible.value = true;
};

const showAddLocation = () => {
  addLocationVisible.value = true;
};

const showJoinGroup = () => {
  joinGroupVisible.value = true;
};

const handleUpdateUserInfo = (userInfo: Partial<UserInfo>) => {
  demoUserInfo.value = { ...demoUserInfo.value, ...userInfo };
  message.success('用户信息更新成功！');
};

const handleUpdateLocation = (locationData: LocationData) => {
  demoLocationData.value = locationData;
  message.success('位置信息更新成功！');
};

const handleAddLocation = (locationInfo: any) => {
  message.success(`成功添加位置：${locationInfo.groupName}`);
};

const handleJoinGroup = (invitationCode: string) => {
  message.success(`成功加入群组，邀请码：${invitationCode}`);
};
</script>

<style scoped lang="less">
.demo-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    font-size: 32px;
    color: #1890ff;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
}

.feature-icon {
  color: #52c41a;
  font-size: 20px;
  margin-right: 12px;
  margin-top: 4px;
}

.feature-text {
  flex: 1;
  
  h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  
  .ant-btn {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.tech-features {
  margin-bottom: 16px;
  
  .ant-tag {
    margin-bottom: 8px;
  }
}

.tech-description {
  p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 12px;
  }
  
  ul {
    color: #666;
    line-height: 1.6;
    
    li {
      margin-bottom: 8px;
    }
  }
}

@media (max-width: 768px) {
  .demo-page {
    padding: 16px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    
    .ant-btn {
      justify-content: center;
    }
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
}
</style>
