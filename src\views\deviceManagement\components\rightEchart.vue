<template>
  <a-card>
    <!-- 顶部环境描述 -->
    <div class="header flex">
      <div class="header-content">
        <span class="location-name">{{ locationName }}</span>
      </div>
      <div class="header-right">
        <template v-for="item in headerItems" :key="item.key">
          <div class="right-content">
            <div class="content-up">{{ item.title }}</div>
            <div class="content-down display-center">
              <template v-if="item.key === 'connection'">
                <WifiOutlined :style="{ color: getSignalColor(selectedDevice?.signalStrength) }" />
                <span class="signal-text">{{ getSignalStatus(selectedDevice) }}</span>
              </template>
              <template v-else>
                {{ getItemValue(item.key) }}
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 空气质量图表 -->
    <div v-if="isAG900">     
      <gaugeChart :value="Number(res.air_quality)" poorLabel="差" excellentLabel="优" detailText="室内空气质量" />
    </div>
    <div v-else class="line-chart-box">
      <template v-for="chart in chartConfigs" :key="chart.title">
        <twolineChart v-bind="getChartProps(chart)" />
      </template>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineProps, watch } from 'vue';
import { WifiOutlined } from '@ant-design/icons-vue';
import type { PropType } from 'vue';

import gaugeChart from './echartsComponent/gaugeChart.vue';
import twolineChart from './echartsComponent/twolineChart.vue';
import { getDeviceLineData } from '../api/deviceManagement.api';

// 导入图标
import tempIcon from '/src/assets/device/wendu.png';
import rhIcon from '/src/assets/device/shidu.png';
import pm25Icon from '/src/assets/device/pm2.5.png';
import co2Icon from '/src/assets/device/co2.png';

// 类型定义
interface DeviceData {
  signalStrength?: number;
  signalLevel?: string;
}

interface ChartData {
  air_quality: string;
  cHEPA_Filters_Life: string;
  properties: Record<string, any>;
  timePoints: string[];
}

interface ChartConfig {
  title: string;
  value: number;
  unit: string;
  status: string;
  iconSrc: string;
  yAxisUnit: string;
}

// 常量定义
const SIGNAL_THRESHOLD = -70;
const MOCK_DATA = {
  outdoor: [12, 12, 12, 23, 34, 45, 23, 23, 23, 23, 23, 23, 23, 12, 12, 23, 23, 34, 23, 12, 23, 12, 21, 18],
  indoor: [23, 12, 12, 23, 23, 34, 23, 12, 23, 12, 21, 18, 12, 12, 12, 23, 34, 45, 23, 23, 23, 23, 23, 23]
};

const headerItems = [
  { key: 'environment', title: 'Environment' },
  { key: 'connection', title: 'Connection' },
  { key: 'fanSpeed', title: 'Fan Speed' },
  { key: 'aqi', title: 'AQI' },
  { key: 'chepaLife', title: 'cHEPA Life' },
  { key: 'group', title: 'Group' },
  { key: 'registrationTime', title: 'Registration Time' }
];

const chartConfigs = [
  {
    title: 'Temp.',
    value: 68,
    unit: '°F',
    status: 'Warm',
    iconSrc: tempIcon,
    yAxisUnit: '°F'
  },
  {
    title: 'RH.',
    value: 70,
    unit: '%',
    status: 'Comfort',
    iconSrc: rhIcon,
    yAxisUnit: '%'
  },
  {
    title: 'PM2.5',
    value: 22,
    unit: '%',
    status: 'Good',
    iconSrc: pm25Icon,
    yAxisUnit: '°F'
  },
  {
    title: 'Co2',
    value: 628,
    unit: 'ppm',
    status: 'Unhealthy',
    iconSrc: co2Icon,
    yAxisUnit: 'ppm'
  }
];

// Props 定义
const props = defineProps({
  productName: String,
  locationName: String,
  deviceId: {
    type: [String, Number],
    required: true
  },
  selectedDevice: {
    type: Object as PropType<DeviceData>,
    default: () => ({})
  }
});

// 响应式数据
const res = ref<ChartData>({
  air_quality: '',
  cHEPA_Filters_Life: '',
  properties: {},
  timePoints: []
});

// 计算属性
const isAG900 = computed(() => !props.productName?.includes('AG2000'));

// 方法定义
const getSignalColor = (strength?: number) =>
  strength && strength > SIGNAL_THRESHOLD ? '#52c41a' : '#ff4d4f';

const getSignalStatus = (device?: DeviceData) =>
  device?.signalLevel || (device?.signalStrength && device.signalStrength > SIGNAL_THRESHOLD ? 'Strong' : 'Weak');

const getItemValue = (key: string) => {
  const values: Record<string, string | number> = {
    environment: 'Indoor',
    fanSpeed: '2000',
    aqi: res.value.air_quality,
    chepaLife: res.value.cHEPA_Filters_Life,
    group: `${props.productName} Group`,
    registrationTime: '2023/09/18'
  };
  return values[key] || '--';
};

const getAirQualityStatus = (value: string | number): string => {
  const numValue = Number(value);
  if (numValue <= 50) return 'Good';
  if (numValue <= 100) return 'Moderate';
  if (numValue <= 150) return 'Unhealthy for Sensitive Groups';
  if (numValue <= 200) return 'Unhealthy';
  if (numValue <= 300) return 'Very Unhealthy';
  return 'Hazardous';
};

const getChartProps = (config: ChartConfig) => ({
  ...config,
  outdoorData: MOCK_DATA.outdoor,
  indoorData: MOCK_DATA.indoor,
  outdoorMaxValue: '23°F',
  indoorMaxValue: '25°F'
});

// 数据获取
const fetchDeviceLineData = async (deviceId: string | number, type = 0) => {
  if (!deviceId) return;
  try {
    const data = await getDeviceLineData(String(deviceId), type);
    res.value = data;
  } catch (error) {
    console.error('Error fetching device data:', error);
  }
};

// 监听器
watch(() => props.deviceId, (newId) => {
  if (newId) fetchDeviceLineData(newId, 0);
}, { immediate: true });

// 生命周期钩子
onMounted(() => {
  console.log('Component mounted with productName:', props.productName);
});
</script>

<style scoped lang="less">
.header {
  .header-content {
    .location-name {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }
  }

  .header-right {
    display: flex;
    flex: 1;
    justify-content: space-evenly;

    .right-content {
      display: flex;
      align-items: center;
      flex-direction: column;

      .content-up {
        font-size: 16px;
        color: #000;
      }

      .content-down {
        height: 50px;
        color: #00A0E9;
        font-size: 14px;

        .signal-text {
          margin-left: 5px;
        }
      }
    }
  }
}

.indoor-label {
  background: rgb(49, 187, 119);
  color: rgb(255, 255, 255);
  width: 110px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 20px;
}

.line-chart-box {
  border-bottom: 1px solid gainsboro;
}
</style>
