export default {
  okText: 'OK',
  closeText: 'Close',
  cancelText: 'Cancel',
  loadingText: 'Loading...',
  saveText: 'Save',
  delText: 'Delete',
  resetText: 'Reset',
  searchText: 'Search',
  queryText: 'Query',
  query: 'Query',
  reset: 'Reset',
  collapse: 'Collapse',
  expand: 'Expand',
  noFile: 'No File',
  download: 'Download',
  batchOperation: 'Batch Operation',

  all: 'All',
  online: 'Online',
  offline: 'Offline',
  yes: 'Yes',
  no: 'No',

  inputText: 'Please enter ',
  chooseText: 'Please choose ',
  onlyOneRecord: 'Only one record can be selected',
  atLeastOneRecord: 'At least one record must be selected',
  upText: 'Unfold',
  downText: 'Fold',
  noImage: 'No Image',
  redo: 'Refresh',
  back: 'Back',
  rowIndex: 'Row Index',

  light: 'Light',
  dark: 'Dark',
  add: 'Add',
  export: 'Export',
  import: 'Import',
  NoSelectedData: 'None Data Selected',
  delete: 'Delete',
  batchdelete: 'Batch Delete',
  selected: 'Selected',
  clear: 'Clear',
  records: 'records',
  spreadable: "(Can be cross-page)",
  confirmDelete: 'Are you sure you want to delete',
  detail: 'Detail',
  edit: 'Edit',
  action: 'Action',
  more: 'More',
  superQuery: {
    equal: 'Equal',
    notEqual: 'Not Equal',
    gt: 'Greater Than',
    ge: 'Greater Than or Equal',
    lt: 'Less Than',
    le: 'Less Than or Equal',
    like: 'Contains',
    notLike: 'Not Contains',
    in: 'In',
    notIn: 'Not In',
    title: 'Advanced Query',
    queryTitle: 'Query Conditions',
    addRule: 'Add Rule',
    addRuleGroup: 'Add Rule Group',
    searchBtn: 'Search',
    resetBtn: 'Reset',
    superQuery: 'Advanced Query',
  },

  workspace_name: "Workspace Name",
  workspace_location: "Workspace Location",
  v_user_workspace: "User Workspace",
  selectPrefix: 'Please Select',
  inputPrefix: 'Please Enter',
  startDate: 'Start Date',
  endDate: 'End Date',
  startTime: 'Start Time',
  endTime: 'End Time',
  startInput: 'Start',
  endInput: 'End',

  v_user_report: "Unified and effective system users",
  username: 'username',
  realname: 'realname',
  sex: 'sex',
  email: 'email',
  phone: 'phone',
  birthday: 'birthday',

  form: {
    cgRpConfigName: "cgRpConfigName",
    v_user_report: "Select system user",
    v_user_workspace: "User workspaces",
    workspace_name: "name",
    workspace_location: "location",
    username: "username",
    realname: "realname",
    sex: "sex",
    email: "email",
    phone: "phone",
    birthday: "birthday",
    status: "status",
  }
};
