<!--
  分组图表组件
  功能：展示分组的空气质量数据，包括仪表盘和折线图
  作者：开发团队
  创建时间：2024
-->
<template>
  <a-card>
    <!-- 顶部分组信息 -->
    <div class="header flex">
      <div class="header-content">
        <span class="group-name">{{ groupName }}</span>
        <div class="group-info">
          <TeamOutlined style="margin-right: 4px;" />
          <span>{{ deviceCount }} devices</span>
        </div>
      </div>
      <div class="header-right">
        <template v-for="item in headerItems" :key="item.key">
          <div class="right-content">
            <div class="content-up">{{ item.title }}</div>
            <div class="content-down display-center">
              <template v-if="item.key === 'connection'">
                <WifiOutlined :style="{ color: getConnectionColor() }" />
                <span class="signal-text">{{ getConnectionStatus() }}</span>
              </template>
              <template v-else>
                {{ getItemValue(item.key) }}
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 空气质量图表区域 -->
    <div class="chart-container">
      <!-- 仪表盘图表 -->
      <div class="gauge-section">
        <gaugeChart 
          :value="Number(airQualityData.overall)" 
          :status="getAirQualityStatus(String(airQualityData.overall))" 
          detailText="Group Air Quality" 
        />
      </div>
      
      <!-- 折线图区域 -->
      <div class="line-chart-section">
        <template v-for="chart in chartConfigs" :key="chart.title">
          <twolineChart v-bind="getChartProps(chart)" />
        </template>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineProps, watch } from 'vue';
import { WifiOutlined, TeamOutlined } from '@ant-design/icons-vue';
import type { PropType } from 'vue';

import gaugeChart from '../../deviceManagement/components/echartsComponent/gaugeChart.vue';
import twolineChart from '../../deviceManagement/components/echartsComponent/twolineChart.vue';

// 导入图标
import tempIcon from '/src/assets/device/wendu.png';
import rhIcon from '/src/assets/device/shidu.png';
import pm25Icon from '/src/assets/device/pm2.5.png';
import co2Icon from '/src/assets/device/co2.png';

// 类型定义
interface GroupData {
  id: string;
  name: string;
  deviceCount: number;
  location: string;
  status: string;
  devices: string[];
}

interface AirQualityData {
  overall: number;
  temperature: number;
  humidity: number;
  pm25: number;
  co2: number;
}

interface ChartConfig {
  title: string;
  value: number;
  unit: string;
  status: string;
  iconSrc: string;
  yAxisUnit: string;
}

// Props定义
const props = defineProps({
  groupName: {
    type: String,
    default: 'Group Name'
  },
  deviceCount: {
    type: Number,
    default: 0
  },
  groupId: {
    type: String,
    default: ''
  }
});

// 常量定义
const MOCK_DATA = {
  outdoor: [50, 45, 48, 52, 55, 58, 52, 48, 45, 47, 50, 53, 55, 48, 45, 52, 55, 58, 52, 48, 50, 45, 48, 52],
  indoor: [3, 5, 4, 6, 8, 12, 8, 5, 3, 4, 6, 8, 3, 5, 4, 6, 8, 12, 8, 5, 3, 5, 4, 6]
};

const headerItems = [
  { key: 'environment', title: 'Environment' },
  { key: 'connection', title: 'Connection' },
  { key: 'fanSpeed', title: 'Fan Speed' },
  { key: 'aqi', title: 'AQI' },
  { key: 'group', title: 'Group' },
  { key: 'lastUpdate', title: 'Last Update' }
];

// 响应式数据
const airQualityData = ref<AirQualityData>({
  overall: 70,
  temperature: 70,
  humidity: 45,
  pm25: 50,
  co2: 400
});

const chartConfigs = ref<ChartConfig[]>([
  {
    title: 'Temp.',
    value: 70,
    unit: '°F',
    status: 'Excellent',
    iconSrc: tempIcon,
    yAxisUnit: '°F'
  },
  {
    title: 'RH.',
    value: 45,
    unit: '%',
    status: 'Excellent',
    iconSrc: rhIcon,
    yAxisUnit: '%'
  },
  {
    title: 'PM2.5',
    value: 50,
    unit: 'μg/m³',
    status: 'Good',
    iconSrc: pm25Icon,
    yAxisUnit: 'μg/m³'
  },
  {
    title: 'Co2',
    value: 400,
    unit: 'ppm',
    status: 'Good',
    iconSrc: co2Icon,
    yAxisUnit: 'ppm'
  }
]);

// 计算属性和方法
const getAirQualityStatus = (value: string) => {
  const numValue = Number(value);
  if (numValue <= 50) return 'Excellent';
  if (numValue <= 100) return 'Good';
  if (numValue <= 150) return 'Moderate';
  if (numValue <= 200) return 'Poor';
  return 'Very Poor';
};

const getConnectionColor = () => {
  // 根据分组中设备的连接状态返回颜色
  return '#52c41a'; // 绿色表示良好连接
};

const getConnectionStatus = () => {
  // 根据分组中设备的连接状态返回状态文本
  return 'Good';
};

const getItemValue = (key: string) => {
  switch (key) {
    case 'environment':
      return 'Indoor';
    case 'fanSpeed':
      return '2';
    case 'aqi':
      return '68%';
    case 'group':
      return props.groupName;
    case 'lastUpdate':
      return '18:00';
    default:
      return '--';
  }
};

const getChartProps = (config: ChartConfig) => ({
  ...config,
  outdoorData: MOCK_DATA.outdoor,
  indoorData: MOCK_DATA.indoor,
  outdoorMaxValue: '50μg/m³',
  indoorMaxValue: '3μg/m³'
});

// 数据获取方法
const fetchGroupData = async (groupId: string) => {
  if (!groupId) return;
  try {
    // 模拟API调用获取分组数据
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 更新空气质量数据
    airQualityData.value = {
      overall: 70,
      temperature: 70,
      humidity: 45,
      pm25: 50,
      co2: 400
    };
    
    // 更新图表配置
    chartConfigs.value = [
      {
        title: 'Temp.',
        value: airQualityData.value.temperature,
        unit: '°F',
        status: 'Excellent',
        iconSrc: tempIcon,
        yAxisUnit: '°F'
      },
      {
        title: 'RH.',
        value: airQualityData.value.humidity,
        unit: '%',
        status: 'Excellent',
        iconSrc: rhIcon,
        yAxisUnit: '%'
      },
      {
        title: 'PM2.5',
        value: airQualityData.value.pm25,
        unit: 'μg/m³',
        status: 'Good',
        iconSrc: pm25Icon,
        yAxisUnit: 'μg/m³'
      },
      {
        title: 'Co2',
        value: airQualityData.value.co2,
        unit: 'ppm',
        status: 'Good',
        iconSrc: co2Icon,
        yAxisUnit: 'ppm'
      }
    ];
  } catch (error) {
    console.error('Error fetching group data:', error);
  }
};

// 监听器
watch(() => props.groupId, (newId) => {
  if (newId) fetchGroupData(newId);
}, { immediate: true });

// 生命周期钩子
onMounted(() => {
  console.log('Group chart component mounted with groupName:', props.groupName);
});
</script>

<style scoped lang="less">
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-name {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.group-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.header-right {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.right-content {
  text-align: center;
  min-width: 80px;
}

.content-up {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.content-down {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.signal-text {
  margin-left: 4px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.gauge-section {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.line-chart-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.display-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
}
</style>
