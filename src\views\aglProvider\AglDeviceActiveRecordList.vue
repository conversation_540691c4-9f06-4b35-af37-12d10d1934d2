<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'aglProvider:agl_device_active_record:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'aglProvider:agl_device_active_record:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'aglProvider:agl_device_active_record:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>  
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'aglProvider:agl_device_active_record:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AglDeviceActiveRecordModal @register="registerModal" @success="handleSuccess"></AglDeviceActiveRecordModal>
  </div>
</template>

<script lang="ts" name="aglProvider-aglDeviceActiveRecord" setup>
  import {ref, reactive, defineAsyncComponent} from 'vue';
  import {BasicTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {columns, searchFormSchema, superQuerySchema} from './AglDeviceActiveRecord.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './AglDeviceActiveRecord.api';
  
  // 动态导入组件
  const AglDeviceActiveRecordModal = defineAsyncComponent(() => import('./components/AglDeviceActiveRecordModal.vue'));
  const queryParam = reactive<any>({});
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '用户激活记录',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
                ['activeTime', ['beginActiveTime', 'endActiveTime'], 'YYYY-MM-DD HH:mm:ss']
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              console.log('beforeFetch 原始参数:', params);
              
              // 先合并参数
              const finalParams = Object.assign(params, queryParam);
              console.log('beforeFetch 合并后参数:', finalParams);
              
              // 添加默认排序
              finalParams.column = 'createTime';
              finalParams.order = 'desc';
              
              console.log('beforeFetch 最终参数:', finalParams);
              return finalParams;
            },
      },
       exportConfig: {
            name:"用户激活记录",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    console.log('handleSuperQuery 原始参数:', params);
    
    console.log('handleSuperQuery 转换后参数:', params);
    
    // 清空之前的查询参数
    Object.keys(queryParam).forEach(key => {
      delete queryParam[key];
    });
    
    // 设置新的查询参数
    Object.keys(params).forEach((k) => {
      queryParam[k] = params[k];
    });
    
    console.log('handleSuperQuery 设置到queryParam:', queryParam);
    
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'aglProvider:agl_device_active_record:edit'
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'aglProvider:agl_device_active_record:delete'
         }
       ]
   }


</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>