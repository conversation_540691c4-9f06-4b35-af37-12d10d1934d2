<!--
  添加/编辑设备位置弹窗组件
  功能：添加新的设备位置或编辑现有设备位置
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="500"
    @ok="handleSave"
    @cancel="handleCancel"
    :confirmLoading="saveLoading"
  >
    <div class="add-location-modal">
      <div class="form-section">
        <!-- 群组名称 -->
        <div class="form-item">
          <label>workspace name <span class="required">*</span></label>
          <a-input 
            v-model:value="formData.workspaceName" 
            placeholder="Enter workspace name"
          />
        </div>

        <!-- 地址选择 -->
        <div class="form-item">
          <label>workspace location <span class="required">*</span></label>
          <div class="address-selector" @click="handleSelectAddress">
            <EnvironmentOutlined class="location-icon" />
            <span class="address-text">
              {{ formData.workspaceLocation || 'select address' }}
            </span>
            <RightOutlined class="arrow-icon" />
          </div>
        </div>

        <!-- 备注 -->
        <div class="form-item">
          <label>remark</label>
          <a-textarea 
            v-model:value="formData.remark" 
            placeholder="Enter remark (optional)"
            :rows="3"
          />
        </div>

        <!-- 位置列表 -->
        <div class="form-item">
          <label>location list ({{ selectedLocations.length }})</label>
          <div class="location-list">
            <div 
              v-for="location in locationOptions" 
              :key="location.id"
              class="location-option"
            >
              <a-checkbox 
                v-model:checked="location.checked"
                @change="handleLocationChange"
              >
                {{ location.name }}
              </a-checkbox>
            </div>
          </div>
          
          <a-button 
            type="primary" 
            size="small" 
            @click="handleAddOtherLocation"
            style="margin-top: 12px;"
          >
            add other location
          </a-button>
        </div>
      </div>
    </div>

    <!-- 自定义底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">cancel</a-button>
        <a-button type="primary" @click="handleSave" :loading="saveLoading">
          save
        </a-button>
      </div>
    </template>
  </a-modal>

  <!-- 地址选择弹窗 - 现在包含Google地图 -->
  <a-modal
    v-model:open="addressModalVisible"
    title="Select Address"
    :width="800"
    :height="600"
    @ok="handleConfirmAddress"
    @cancel="handleCancelAddress"
  >
    <div class="address-form">
      <!-- 地址搜索框 -->
      <div class="search-section">
        <a-input 
          v-model:value="searchAddress" 
          placeholder="搜索地址或点击地图选择位置"
          @keyup.enter="handleSearchAddress"
        />
        <a-button type="primary" @click="handleSearchAddress" style="margin-left: 8px;">
          搜索
        </a-button>
      </div>
      
      <!-- Google地图容器 -->
      <div ref="mapContainer" class="map-container"></div>
      
      <!-- 选中的地址信息 -->
      <div v-if="selectedLocationInfo" class="selected-info">
        <h4>选中位置信息:</h4>
        <p><strong>地址:</strong> {{ selectedLocationInfo.address }}</p>
        <p><strong>经度:</strong> {{ selectedLocationInfo.lng?.toFixed(6) }}</p>
        <p><strong>纬度:</strong> {{ selectedLocationInfo.lat?.toFixed(6) }}</p>
      </div>
    </div>
  </a-modal>

  <!-- 添加其他位置弹窗 -->
  <a-modal
    v-model:open="addLocationModalVisible"
    title="Add Other Location"
    :width="400"
    @ok="handleConfirmAddLocation"
    @cancel="handleCancelAddLocation"
  >
    <div class="add-location-form">
      <a-input 
        v-model:value="newLocationName" 
        placeholder="Enter location name"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { 
  EnvironmentOutlined, 
  RightOutlined 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { addWorkSpaceAndRoom } from '/@/views/aglWorkspace/AglWorkspace.api';
import { 
  loadGoogleMaps, 
  createMap, 
  createGeocoder, 
  createPlacesService, 
  createMarker,
  getGoogleMaps 
} from '/@/utils/googleMaps';

// Props 定义
interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'add': [locationInfo: any];
  'success': [];
}>();

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const formData = ref({
  id: '',
  workspaceName: '',
  workspaceLocation: '',
  longitude: null as number | null,
  latitude: null as number | null,
  remark: ''
});

const locationOptions = ref([
  { id: 1, name: 'Living Room', checked: true },
  { id: 2, name: 'Master Bedroom', checked: true },
  { id: 3, name: 'Second Bedroom', checked: true },
  { id: 4, name: 'Office', checked: false },
  { id: 5, name: 'Conference Room', checked: false }
]);

const addressModalVisible = ref(false);
const addLocationModalVisible = ref(false);
const newLocationName = ref('');
const saveLoading = ref(false);

// Google地图相关数据
const mapContainer = ref<HTMLDivElement | null>(null);
const searchAddress = ref('');
const selectedLocationInfo = ref<{
  address: string;
  lat: number;
  lng: number;
} | null>(null);

let googleMap: any = null;
let currentMarker: any = null;
let geocoder: any = null;
let placesService: any = null;

// 计算属性
const selectedLocations = computed(() => {
  return locationOptions.value.filter(location => location.checked);
});

// 动态标题
const modalTitle = computed(() => {
  return 'Add Device Location';
});

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    // 弹窗关闭时重置表单
    resetForm();
  }
});

// 初始化Google地图
const initGoogleMap = async () => {
  try {
    await loadGoogleMaps();
    await nextTick();
    
    if (!mapContainer.value) return;

    // 初始化地图（默认定位到北京）
    const defaultLocation = { lat: 39.9042, lng: 116.4074 };
    
    googleMap = createMap(mapContainer.value, {
      zoom: 15,
      center: defaultLocation,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
    });

    // 初始化地理编码器
    geocoder = createGeocoder();
    
    // 初始化Places服务
    placesService = createPlacesService(googleMap);

    // 添加地图点击事件
    const googleMaps = getGoogleMaps();
    googleMaps.event.addListener(googleMap, 'click', (event: any) => {
      handleMapClick(event.latLng);
    });

    // 尝试获取用户当前位置
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          googleMap.setCenter(userLocation);
          googleMap.setZoom(15);
        },
        () => {
          console.log('无法获取用户位置，使用默认位置');
        }
      );
    }

  } catch (error) {
    console.error(JSON.stringify(error));
    message.warning('Google地图初始化失败，请检查网络连接或API配置');
  }
};

// 处理地图点击事件
const handleMapClick = (latLng: any) => {
  const lat = latLng.lat();
  const lng = latLng.lng();

  // 移除之前的标记
  if (currentMarker) {
    currentMarker.setMap(null);
  }

  // 添加新标记
  currentMarker = createMarker({
    position: { lat, lng },
    map: googleMap,
    title: '选中位置',
  });

  // 反向地理编码获取地址
  geocoder.geocode({ location: { lat, lng } }, (results: any, status: any) => {
    if (status === 'OK' && results[0]) {
      const address = results[0].formatted_address;
      selectedLocationInfo.value = {
        address,
        lat,
        lng,
      };
    } else {
      selectedLocationInfo.value = {
        address: `位置: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
        lat,
        lng,
      };
    }
  });
};

// 处理地址搜索
const handleSearchAddress = () => {
  if (!searchAddress.value.trim()) {
    message.warning('请输入要搜索的地址');
    return;
  }

  const googleMaps = getGoogleMaps();
  const request = {
    query: searchAddress.value,
    fields: ['name', 'geometry', 'formatted_address'],
  };

  placesService.findPlaceFromQuery(request, (results: any, status: any) => {
    if (status === googleMaps.places.PlacesServiceStatus.OK && results[0]) {
      const place = results[0];
      const location = place.geometry.location;
      
      // 移动地图到搜索结果
      googleMap.setCenter(location);
      googleMap.setZoom(15);
      
      // 模拟点击事件
      handleMapClick(location);
    } else {
      message.error('未找到该地址，请尝试其他关键词');
    }
  });
};

// 监听地址选择弹窗的打开状态
watch(addressModalVisible, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initGoogleMap();
    });
  }
});

// 方法定义
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const handleSave = async () => {
  // 表单验证
  if (!formData.value.workspaceName.trim()) {
    message.error('请输入工作空间名称');
    return;
  }

  if (!formData.value.workspaceLocation.trim()) {
    message.error('请选择工作空间地址');
    return;
  }

  if (selectedLocations.value.length === 0) {
    message.error('Please select at least one location');
    return;
  }

  try {
    saveLoading.value = true;

    // 收集选中的房间名称
    const selectedRooms = selectedLocations.value.map(location => location.name);

    // 准备API请求数据
    const requestData = {
      workspaceName: formData.value.workspaceName.trim(),
      workspaceLocation: formData.value.workspaceLocation.trim(),
      longitude: formData.value.longitude || 0,
      latitude: formData.value.latitude || 0,
      remark: formData.value.remark?.trim() || '',
      rooms: selectedRooms
    };

    console.log('提交工作空间和房间数据:', requestData);

    // 调用新的添加工作空间和房间API接口
    await addWorkSpaceAndRoom(requestData);

    // 成功处理
    message.success('工作空间和房间添加成功');
    
    // 触发成功事件
    emit('success');
    
    const resultData = {
      ...requestData,
      locations: selectedRooms,
      isUsingMockData: false
    };

    emit('add', resultData);

    // 关闭弹窗并重置表单
    modalVisible.value = false;
    resetForm();

  } catch (error) {
    console.error('保存工作空间和房间失败:', error);
    message.error('保存失败，请检查网络连接或联系管理员');
  } finally {
    saveLoading.value = false;
  }
};

const handleSelectAddress = () => {
  addressModalVisible.value = true;
  searchAddress.value = formData.value.workspaceLocation || '';
  selectedLocationInfo.value = null;
};

const handleConfirmAddress = () => {
  if (selectedLocationInfo.value) {
    formData.value.workspaceLocation = selectedLocationInfo.value.address;
    formData.value.longitude = selectedLocationInfo.value.lng;
    formData.value.latitude = selectedLocationInfo.value.lat;
    message.success('地址选择成功');
  } else {
    message.warning('请选择一个位置或使用模拟数据');
    return;
  }
  addressModalVisible.value = false;
};

const handleCancelAddress = () => {
  addressModalVisible.value = false;
  selectedLocationInfo.value = null;
  // 清理地图标记
  if (currentMarker) {
    currentMarker.setMap(null);
    currentMarker = null;
  }
};

const handleLocationChange = () => {
  // 位置选择变化处理
};

const handleAddOtherLocation = () => {
  addLocationModalVisible.value = true;
};

const handleConfirmAddLocation = () => {
  if (!newLocationName.value.trim()) {
    message.error('Please enter location name');
    return;
  }

  const newId = Math.max(...locationOptions.value.map(loc => loc.id)) + 1;
  locationOptions.value.push({
    id: newId,
    name: newLocationName.value.trim(),
    checked: true
  });

  message.success('Location added successfully');
  addLocationModalVisible.value = false;
  newLocationName.value = '';
};

const handleCancelAddLocation = () => {
  addLocationModalVisible.value = false;
  newLocationName.value = '';
};

const resetForm = () => {
  formData.value = {
    id: '',
    workspaceName: '',
    workspaceLocation: '',
    longitude: null,
    latitude: null,
    remark: ''
  };
  
  // 重置位置选择
  locationOptions.value.forEach(location => {
    if (['Living Room', 'Master Bedroom', 'Second Bedroom'].includes(location.name)) {
      location.checked = true;
    } else {
      location.checked = false;
    }
  });
  
  // 清理地图相关数据
  selectedLocationInfo.value = null;
  searchAddress.value = '';
  saveLoading.value = false;
  if (currentMarker) {
    currentMarker.setMap(null);
    currentMarker = null;
  }
};
</script>

<style scoped lang="less">

.form-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-item {
  display: flex;
  flex-direction: column;

  label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;

    .required {
      color: #ff4d4f;
      margin-left: 4px;
    }
  }
}

.address-selector {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #1890ff;
  }
}

.location-icon {
  color: #ff4d4f;
  margin-right: 8px;
  font-size: 16px;
}

.address-text {
  flex: 1;
  color: #666;
}

.arrow-icon {
  color: #ccc;
  font-size: 12px;
}

.location-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.location-option {
  display: flex;
  align-items: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.address-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-section {
  display: flex;
  align-items: center;
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #f5f5f5;
}

.selected-info {
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;

  h4 {
    margin: 0 0 8px 0;
    color: #52c41a;
    font-size: 14px;
  }

  p {
    margin: 4px 0;
    font-size: 12px;
    color: #333;
  }
}

.add-location-form {
  padding: 20px 0;
}
</style>
