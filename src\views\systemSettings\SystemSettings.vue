<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('routes.airgle.systemSettings') }}</h1>
    <div class="settings-list-card">
      <div
        v-for="item in settingsList"
        :key="item.key"
        class="settings-list-row"
        @click="handleRowClick(item)"
        style="cursor: pointer;"
      >
        <span class="settings-list-label">{{ item.label }}</span>
        <span class="settings-list-value">
          <template v-if="item.type === 'switch'">
            <input type="checkbox" :checked="!!item.value" disabled />
          </template>
          <template v-else-if="item.type === 'select'">
            <span>{{ item.value }}</span>
          </template>
          <template v-else>
            <span class="settings-list-arrow">&#8250;</span>
          </template>
        </span>
      </div>
      <BasicModal @register="registerModal" :title="modalTitle" :width="600" @ok="handleOk" okText="修改">
        <template v-if="modalType === 'persona'">
          <div style="padding: 24px 32px;">
            <div style="display: flex; flex-direction: column; gap: 18px;">
              <div><b>姓名：</b>{{ userDetail.realname || '-' }}</div>
              <div><b>性别：</b>{{ userDetail.sex === 1 ? '男' : userDetail.sex === 2 ? '女' : '-' }}</div>
              <div><b>生日：</b>{{ userDetail.birthday || '-' }}</div>
              <div><b>职位：</b>{{ userDetail.post_dictText || userDetail.post || '-' }}</div>
              <div><b>邮箱：</b>{{ userDetail.email || '-' }}</div>
              <div><b>手机：</b>{{ userDetail.phone || '-' }}</div>
            </div>
          </div>
        </template>
        <template v-else-if="modalType === 'account'">
          <div style="padding: 24px 32px;">
            <div style="display: flex; flex-direction: column; gap: 18px;">
              <div><b>手机：</b>{{ userDetail.phoneText || userDetail.phone || '-' }}</div>
              <div><b>邮箱：</b>{{ userDetail.email || '-' }}</div>
              <div><b>密码：</b>********</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div style="text-align:center;padding:40px 0;color:#888;">这里是弹窗内容（可自定义）</div>
        </template>
      </BasicModal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { BasicModal, useModal } from '/@/components/Modal';
import { useRouter } from 'vue-router';
import { getUserData } from '/@/views/system/usersetting/UserSetting.api';

const { t } = useI18n();
const [registerModal, { openModal }] = useModal();
const modalType = ref('');
const modalTitle = ref('设置项');
const router = useRouter();
const userDetail = ref<any>({});

const languageText = ref('English');

const settingsList = computed(() => [
  { key: 'persona', label: t('systemSettings.persona'), type: 'arrow' },
  { key: 'account', label: t('systemSettings.account'), type: 'arrow' },
  { key: 'device', label: t('systemSettings.device'), type: 'arrow' },
  { key: 'touchTone', label: t('systemSettings.touchTone'), type: 'switch', value: false },
  { key: 'notification', label: t('systemSettings.notification'), type: 'arrow' },
  { key: 'temperature', label: t('systemSettings.temperature'), type: 'select', value: '℃' },
  { key: 'debug', label: t('systemSettings.debug'), type: 'select', value: languageText.value },
  { key: 'features', label: t('systemSettings.features'), type: 'arrow' },
  { key: 'about', label: t('systemSettings.about'), type: 'arrow' },
  { key: 'privacy', label: t('systemSettings.privacy'), type: 'arrow' },
  { key: 'policy', label: t('systemSettings.policy'), type: 'arrow' },
  { key: 'network', label: t('systemSettings.network'), type: 'arrow' },
  { key: 'cache', label: t('systemSettings.cache'), type: 'arrow' },
]);

function handleRowClick(item: any) {
  if (item.type === 'arrow') {
    if (item.key === 'persona') {
      modalTitle.value = '个人信息';
      modalType.value = 'persona';
      openModal(true, { title: modalTitle.value });
    } else if (item.key === 'account') {
      modalTitle.value = '账号与安全';
      modalType.value = 'account';
      openModal(true, { title: modalTitle.value });
    } else if (item.key === 'notification') {
      router.push({ path: '/system/alerts' });
      return;
    } else {
      modalTitle.value = item.label;
      modalType.value = '';
      openModal(true, { title: modalTitle.value });
    }
  } else if (item.key === 'debug') {
    console.log('Language clicked');
    languageText.value = languageText.value === 'English' ? '中文' : 'English';
  }
}

async function handleOk() {
  if (modalType.value === 'account') {
    router.push({ path: '/system/usersetting', query: { tab: '3' } });
    return;
  }
  if (modalType.value === 'persona') {
    router.push({ path: '/system/usersetting', query: { tab: '1' } });
    return;
  }
}

onMounted(async () => {
  const res = await getUserData();
  if (res && res.success) {
    userDetail.value = res.result;
    if (userDetail.value.phone) {
      userDetail.value.phoneText = userDetail.value.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
  }
});
</script>

<style scoped>
  /* 页面容器 */
  .page-container {
    padding: 0px 24px;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
  }
.settings-list-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  width: 100%;
  margin: 32px auto 0 auto;
  padding: 0 0 24px 0;
  max-width: 100%;
}
.settings-list-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 32px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
}
.settings-list-row:last-child {
  border-bottom: none;
}
.settings-list-label {
  color: #222;
}
.settings-list-value {
  color: #888;
  display: flex;
  align-items: center;
}
.settings-list-arrow {
  margin-left: 8px;
  font-size: 18px;
  color: #bbb;
}
</style>
