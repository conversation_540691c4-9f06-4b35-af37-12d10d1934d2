<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="AglHelpFeedbackQuestionForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="反馈用户" v-bind="validateInfos.userName" id="AglHelpFeedbackQuestionForm-userName" name="userName">
								<a-input v-model:value="formData.userName" placeholder="请输入反馈用户" disabled allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="问题分类" v-bind="validateInfos.feedbackCategory" id="AglHelpFeedbackQuestionForm-feedbackCategory" name="feedbackCategory">
								<j-category-select v-model:value="formData.feedbackCategory" pcode="C04" placeholder="请选择问题分类"   @change="(value) => handleFormChange('feedbackCategory', value)" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="问题描述" v-bind="validateInfos.feedbackContent" id="AglHelpFeedbackQuestionForm-feedbackContent" name="feedbackContent">
								<a-textarea v-model:value="formData.feedbackContent" :rows="4" placeholder="请输入问题描述" />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="问题发生时间" v-bind="validateInfos.questionTime" id="AglHelpFeedbackQuestionForm-questionTime" name="questionTime">
								<a-date-picker placeholder="请选择问题发生时间"  v-model:value="formData.questionTime" showTime value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="用户手机" v-bind="validateInfos.userPhone" id="AglHelpFeedbackQuestionForm-userPhone" name="userPhone">
								<a-input v-model:value="formData.userPhone" placeholder="请输入用户手机"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="用户邮箱" v-bind="validateInfos.userEmail" id="AglHelpFeedbackQuestionForm-userEmail" name="userEmail">
								<a-input v-model:value="formData.userEmail" placeholder="请输入用户邮箱"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="问题频率" v-bind="validateInfos.questionFrequency" id="AglHelpFeedbackQuestionForm-questionFrequency" name="questionFrequency">
								<j-dict-select-tag v-model:value="formData.questionFrequency" dictCode="agl_question_frequency" placeholder="请选择问题频率"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="同意授权" v-bind="validateInfos.isAgree" id="AglHelpFeedbackQuestionForm-isAgree" name="isAgree">
								<j-dict-select-tag type='radio' v-model:value="formData.isAgree" dictCode="yn" placeholder="请选择同意授权"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="图片或视频" v-bind="validateInfos.attach" id="AglHelpFeedbackQuestionForm-attach" name="attach">
								<j-upload v-model:value="formData.attach"   ></j-upload>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="处理状态" v-bind="validateInfos.handleStatus" id="AglHelpFeedbackQuestionForm-handleStatus" name="handleStatus">
								<j-dict-select-tag v-model:value="formData.handleStatus" dictCode="agl_question_status" placeholder="请选择处理状态"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="处理记录" v-bind="validateInfos.handleRecord" id="AglHelpFeedbackQuestionForm-handleRecord" name="handleRecord">
								<a-input v-model:value="formData.handleRecord" placeholder="请输入处理记录"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="处理人" v-bind="validateInfos.handleUser" id="AglHelpFeedbackQuestionForm-handleUser" name="handleUser">
								<a-input v-model:value="formData.handleUser" placeholder="请输入处理人"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="处理时间" v-bind="validateInfos.handleTime" id="AglHelpFeedbackQuestionForm-handleTime" name="handleTime">
								<a-date-picker placeholder="请选择处理时间"  v-model:value="formData.handleTime" showTime value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JCategorySelect from '/@/components/Form/src/jeecg/components/JCategorySelect.vue';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../AglHelpFeedbackQuestion.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    userName: '',   
    feedbackCategory: '',   
    feedbackContent: '',   
    questionTime: '',   
    userPhone: '',   
    userEmail: '',   
    questionFrequency: '',   
    isAgree: '',   
    attach: '',   
    handleStatus: '',   
    handleRecord: '',   
    handleUser: '',   
    handleTime: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    handleStatus: [{ required: true, message: '请输入处理状态!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  /**
   * 值改变事件触发
   * @param key
   * @param value
   */
  function handleFormChange(key, value) {
    formData[key] = value;
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
