<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'aglProvider:agl_alerts:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'aglProvider:agl_alerts:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'aglProvider:agl_alerts:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'aglProvider:agl_alerts:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AglAlertsModal @register="registerModal" @success="handleSuccess"></AglAlertsModal>
    <!-- 串货记录详情 -->
    <SuspiciousRecordModal @register="registerSuspiciousModal" @success="handleSuccess"></SuspiciousRecordModal>
  </div>
</template>

<script lang="ts" name="aglProvider-aglAlerts" setup>
  import {ref, reactive, defineAsyncComponent} from 'vue';
  import {BasicTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {columns, searchFormSchema, superQuerySchema} from './AglAlerts.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './AglAlerts.api';
  import { useUserStore } from '/@/store/modules/user';
  
  // 导入组件
  import AglAlertsModal from './components/AglAlertsModal.vue';
  import SuspiciousRecordModal from './components/SuspiciousRecordModal.vue';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册串货记录modal
  const [registerSuspiciousModal, {openModal: openSuspiciousModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '串货告警记录',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
      },
       exportConfig: {
            name:"串货告警记录",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      if (k === 'sentTime' && params[k] && Array.isArray(params[k]) && params[k].length === 2) {
        // 处理时间范围，转换为beginTime和endTime
        queryParam.beginTime = params[k][0];
        queryParam.endTime = params[k][1];
      } else {
        queryParam[k] = params[k];
      }
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'aglProvider:agl_alerts:edit'
         },
         {
           label: '串货记录',
           onClick: handleViewSuspicious.bind(null, record),
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'aglProvider:agl_alerts:delete'
         }
       ]
   }

   /**
    * 查看串货记录
    */
  function handleViewSuspicious(record: Recordable) {
     // 检查是否有串货记录数据
     if (record.suspiciousId) {
       openSuspiciousModal(true, {
         record: {
           // 串货记录的基本信息
           suspiciousId: record.suspiciousId,
           // 串货记录的关联数据（假设这些字段在record中）
           deviceUuid: record.deviceUuid,
           providerName: record.providerName,
           providerLocation: record.providerLocation,
           userId: record.userId,
           userAccount: record.userAccount,
           userNick: record.userNick,
           providerId: record.providerId,
           activeIp: record.activeIp,
           activeLocation: record.activeLocation,
           status: record.status,
           verifiUserId: record.verifiUserId,
           verifiUserName: record.verifiUserName,
           verifiTime: record.verifiTime,
           remark: record.remark,
         },
         isUpdate: false,
         showFooter: false,
         title: '串货记录详情',
         width: 1000,
       });
     } else {
       // 如果没有串货记录数据，可以显示提示
       console.warn('未找到串货记录数据');
     }
   }


</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>