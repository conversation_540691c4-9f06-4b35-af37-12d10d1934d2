import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '设备UUID',
    align:"center",
    dataIndex: 'deviceUuid'
   },
   {
    title: '设备绑定供应商',
    align:"center",
    dataIndex: 'providerName'
   },
   {
    title: '供应商销售地区',
    align:"center",
    dataIndex: 'providerLocation'
   },
   {
    title: '用户账号',
    align:"center",
    dataIndex: 'userAccount'
   },
   {
    title: '用户昵称',
    align:"center",
    dataIndex: 'userNick'
   },
   {
    title: '设备激活ip',
    align:"center",
    dataIndex: 'activeIp'
   },
   {
    title: 'ip所属区域',
    align:"center",
    dataIndex: 'activeLocation'
   },
   {
    title: '核实状态',
    align:"center",
    dataIndex: 'status_dictText'
   },
   {
    title: '核实人名称',
    align:"center",
    dataIndex: 'verifiUserName'
   },
   {
    title: '核实时间',
    align:"center",
    dataIndex: 'verifiTime'
   },
   {
    title: '核实备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "设备UUID",
      field: 'deviceUuid',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "设备绑定供应商",
      field: 'providerName',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "供应商销售地区",
      field: 'providerLocation',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "用户账号",
      field: 'userAccount',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "用户昵称",
      field: 'userNick',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "设备激活ip",
      field: 'activeIp',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "ip所属区域",
      field: 'activeLocation',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "核实状态",
      field: 'status',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"verifi_status"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '设备UUID',
    field: 'deviceUuid',
    component: 'Input',
  },
  {
    label: '设备绑定供应商',
    field: 'providerName',
    component: 'Input',
  },
  {
    label: '供应商销售地区',
    field: 'providerLocation',
    component: 'Input',
  },
  {
    label: '用户账号',
    field: 'userAccount',
    component: 'Input',
  },
  {
    label: '用户昵称',
    field: 'userNick',
    component: 'Input',
  },
  {
    label: '设备激活ip',
    field: 'activeIp',
    component: 'Input',
  },
  {
    label: 'ip所属区域',
    field: 'activeLocation',
    component: 'Input',
  },
  {
    label: '核实状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"verifi_status"
     },
  },
  {
    label: '核实人名称',
    field: 'verifiUserName',
    component: 'Input',
  },
  {
    label: '核实时间',
    field: 'verifiTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '核实备注',
    field: 'remark',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  deviceUuid: {title: '设备UUID',order: 0,view: 'text', type: 'string',},
  providerName: {title: '设备绑定供应商',order: 1,view: 'text', type: 'string',},
  providerLocation: {title: '供应商销售地区',order: 2,view: 'text', type: 'string',},
  userAccount: {title: '用户账号',order: 3,view: 'text', type: 'string',},
  userNick: {title: '用户昵称',order: 4,view: 'text', type: 'string',},
  activeIp: {title: '设备激活ip',order: 5,view: 'text', type: 'string',},
  activeLocation: {title: 'ip所属区域',order: 6,view: 'text', type: 'string',},
  status: {title: '核实状态',order: 7,view: 'list', type: 'string',dictCode: 'verifi_status',},
  verifiUserName: {title: '核实人名称',order: 8,view: 'text', type: 'string',},
  verifiTime: {title: '核实时间',order: 9,view: 'datetime', type: 'string',},
  remark: {title: '核实备注',order: 10,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}