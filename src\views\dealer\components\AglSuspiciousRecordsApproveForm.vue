<template>
  <div style="min-height: 20%;min-width: 50%;">
    <BasicForm @register="registerForm"></BasicForm>
    <div style="width: 100%;text-align: center" v-if="!formDisabled">
      <a-button @click="submitForm" pre-icon="ant-design:check" type="primary">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineExpose } from 'vue';
import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
import { computed, defineComponent, onMounted } from 'vue';
import { propTypes } from '/@/utils/propTypes';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';


export default defineComponent({
  name: "AglSuspiciousRecordsApproveForm",
  components: {
    BasicForm
  },
  props: {
    formData: propTypes.object.def({}),
    formBpm: propTypes.bool.def(true),
  },
  setup(props, { emit }) {
    function handleSubmit() {
      return submitForm();
    }

    defineExpose({
      handleSubmit,
    });
    
    const { createMessage } = useMessage();
    // 基础表单配置
    const formSchema: FormSchema[] = [
      {
        field: 'id',
        component: 'Input',
        label: 'ID',
        show: false,
      },
      {
        field: 'status',
        component: 'JDictSelectTag',
        label: '核实结果',
        required: true,
        componentProps: {
          dictCode: 'verifi_status',
        },
      },
      {
        field: 'approveRemark',
        component: 'InputTextArea',
        label: '备注',
        componentProps: {
          rows: 5,
          maxLength: 250,
        },
      },
    ];

const [registerForm, { setProps, getFieldsValue, setFieldsValue }] = useForm({
      labelWidth: 150,
      schemas: formSchema,
      showActionButtonGroup: false,
      baseColProps: { span: 24 }
    });

const formDisabled = computed(() => {
  // 明确为 true 才禁用，否则启用
  return props.formData.disabled === true;
});

    let formData = {};

    // 初始化表单数据
      async function initFormData() {
        // 设置表单禁用状态
        await setProps({ disabled: formDisabled.value });
        
        // 设置初始值（如果有）
        if (props.formData.status || props.formData.approveRemark) {
          const initialValues = {};
          if (props.formData.status) {
            initialValues.status = props.formData.status;
          }
          if (props.formData.approveRemark) {
            initialValues.approveRemark = props.formData.approveRemark;
          }
          // 需要等待一下再设置初始值
          setTimeout(async () => {
            await setFieldsValue(initialValues);
          }, 100);
        }
      }

    // 提交表单
    async function submitForm() {
      console.log('formData:', props.formData);
      try {
        let data = getFieldsValue();
        // 获取 ids，支持数组或单个
        let ids = props.formData.ids;
        if (!ids || (Array.isArray(ids) && ids.length === 0)) {
          createMessage.error('请选择要核实的记录');
          return;
        }
        // 支持数组或单个，最终转成逗号分隔字符串
        const idsStr = Array.isArray(ids) ? ids.join(',') : ids;
        const params = {
          ids: idsStr,
          remark: data.approveRemark,
          status: data.status,
        };
        await defHttp.post({
          url: '/aglProvider/aglSuspiciousRecords/verifyBatch',
          params,
        }, { joinParamsToUrl: true });
        createMessage.success('批量核实成功');
        emit('success'); // 提交成功后通知父弹窗关闭和刷新
      } catch (error) {
        createMessage.error('提交失败');
        console.error('提交失败:', error);
      }
    }

    // 组件挂载时初始化
    onMounted(() => {
      initFormData();
    });

    return {
      registerForm,
      formDisabled,
      submitForm,
    };
  }
});
</script>

<style scoped>
/* 组件样式 */
</style>