<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'aglProvider:agl_suspicious_records:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button type="primary"  @click="handleBatchVerify" preIcon="ant-design:check-outlined"> 核实</a-button>
          <a-button  type="primary" v-auth="'aglProvider:agl_suspicious_records:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'aglProvider:agl_suspicious_records:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'aglProvider:agl_suspicious_records:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AglSuspiciousRecordsModal @register="registerModal" @success="handleSuccess"></AglSuspiciousRecordsModal>
    <!-- 批量核实弹窗 -->
    <BasicModal 
      v-bind="$attrs" 
      @register="registerVerifyModal" 
      title="可疑单货记录核实" 
      width="40%" 
      @ok="handleVerifySubmit"
      @cancel="handleVerifyCancel"
    >
  <AglSuspiciousRecordsApproveForm 
    ref="verifyFormRef" 
    :key="verifyFormKey"
    :formData="verifyFormData" 
    @success="handleVerifySuccess"
  />
    </BasicModal>
  </div>
</template>

<script lang="ts" name="aglProvider-aglSuspiciousRecords" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import {BasicModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import { useMessage } from '/@/hooks/web/useMessage';
  import AglSuspiciousRecordsModal from './components/AglSuspiciousRecordsModal.vue'
  import AglSuspiciousRecordsApproveForm from '../dealer/components/AglSuspiciousRecordsApproveForm.vue'
  import {columns, searchFormSchema, superQuerySchema} from './AglSuspiciousRecords.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './AglSuspiciousRecords.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  
  
  const { createMessage } = useMessage();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const verifyFormRef = ref();
  const verifyFormData = ref({});
  
  //注册model
  const [registerModal, {openModal}] = useModal();
  const [registerVerifyModal, {openModal: openVerifyModal, closeModal: closeVerifyModal}] = useModal();
  
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '可疑串货记录',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
      },
       exportConfig: {
            name:"可疑串货记录",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
  const verifyFormKey = ref(0); // 添加这个
  /**
   * 批量核实事件
   */
  function handleBatchVerify() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warn('请选择要核实的记录');
      return;
    }
      // 强制重新渲染表单组件
  verifyFormKey.value++;
      // 获取选中行的数据
    const selectedRows = tableContext[2].selectedRows.value;
    // 如果只选中一条记录，预填充数据
    let initialData = {};
    if (selectedRows.length === 1) {
      const record = selectedRows[0];
      initialData = {
        status: record.status,        // 核实状态
        approveRemark: record.remark  // 核实备注（注意字段映射）
      };
    }
    
  // 设置表单数据
  verifyFormData.value = {
    ids: selectedRowKeys.value,
    disabled: false,
    ...initialData  // 预填充的数据
  };
  
    
    // 打开核实弹窗
    openVerifyModal(true, {});
  }
  
  /**
   * 核实弹窗提交
   */
  function handleVerifySubmit() {
    if (verifyFormRef.value && verifyFormRef.value.handleSubmit) {
      verifyFormRef.value.handleSubmit();
    }
  }
  
/**
 * 核实弹窗取消
 */
function handleVerifyCancel() {
  verifyFormData.value = {}; // 重置数据
  closeVerifyModal();
}

/**
 * 核实成功回调
 */
function handleVerifySuccess() {
  verifyFormData.value = {}; // 重置数据
  closeVerifyModal();
  handleSuccess(); // 刷新列表
}
  
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'aglProvider:agl_suspicious_records:edit'
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'aglProvider:agl_suspicious_records:delete'
         }
       ]
   }


</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>