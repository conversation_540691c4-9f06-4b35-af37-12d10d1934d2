import { ref } from 'vue';

type Timer = ReturnType<typeof setTimeout>;

export interface UseDebounceReturn {
  debounce: <T extends (...args: any[]) => any>(fn: T, delay: number) => (...args: Parameters<T>) => void;
}

export function useDebounce(): UseDebounceReturn {
  const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
    const timer = ref<Timer | null>(null);
    
    return (...args: Parameters<T>) => {
      if (timer.value) {
        clearTimeout(timer.value);
      }
      
      timer.value = setTimeout(() => {
        fn(...args);
        timer.value = null;
      }, delay);
    };
  };

  return {
    debounce,
  };
} 