import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '反馈用户',
    align:"center",
    dataIndex: 'userName'
   },
   {
    title: '建议分类',
    align:"center",
    sorter: true,
    dataIndex: 'suggestionCategory',
    customRender:({text}) => {
       return  render.renderCategoryTree(text,'C05')
   },
   },
   {
    title: '图片或者视频',
    align:"center",
    dataIndex: 'attach',
   },
   {
    title: '用户手机',
    align:"center",
    dataIndex: 'userPhone'
   },
   {
    title: '用户邮箱',
    align:"center",
    dataIndex: 'userEmail'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "反馈用户",
      field: "userName",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
	{
      label: "建议分类",
      field: 'suggestionCategory',
      component: 'JCategorySelect',
      componentProps:{
          pcode:"C05",//back和事件未添加，暂时有问题
      },
      //colProps: {span: 6},
 	},
     {
      label: "用户手机",
      field: "userPhone",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
     {
      label: "用户邮箱",
      field: "userEmail",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '反馈用户',
    field: 'userName',
    component: 'Input',
    dynamicDisabled:true
  },
  {
    label: '建议分类',
    field: 'suggestionCategory',
    component: 'JCategorySelect',
    componentProps:{
       pcode:"C05", //TODO back和事件未添加，暂时有问题
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入建议分类!'},
          ];
     },
  },
  {
    label: '建议描述',
    field: 'suggestionContent',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入建议描述!'},
          ];
     },
  },
  {
    label: '图片或者视频',
    field: 'attach',
    component: 'JUpload',
    componentProps:{
     },
  },
  {
    label: '用户手机',
    field: 'userPhone',
    component: 'Input',
  },
  {
    label: '用户邮箱',
    field: 'userEmail',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  userName: {title: '反馈用户',order: 0,view: 'text', type: 'string',},
  suggestionCategory: {title: '建议分类',order: 1,view: 'cat_tree', type: 'string',pcode: 'C05',},
  attach: {title: '图片或者视频',order: 3,view: 'file', type: 'string',},
  userPhone: {title: '用户手机',order: 4,view: 'text', type: 'string',},
  userEmail: {title: '用户邮箱',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}