<!--
  设备位置设置弹窗组件
  功能：编辑和管理设备位置信息
-->
<template>
  <a-modal v-model:open="modalVisible" title="Device Location Settings" :width="600" @ok="handleSave"
    @cancel="handleCancel" :confirmLoading="saveLoading">
    <div class="device-location-modal">
      <!-- 编辑表单 -->
      <div class="form-section">
        <!-- workspace name -->
        <div class="form-item">
          <label>Name <span class="required">*</span></label>
          <a-input v-model:value="formData.workspaceName" placeholder="Enter workspace name" />
        </div>

        <!-- location list -->
        <div class="form-item">
          <div class="location-header">
            <label>Location Management</label>
            <div class="add-location-section">
              <a-button type="dashed" block @click="handleAddOtherLocation" class="add-location-btn">
                <PlusOutlined />
                Add Location
              </a-button>
            </div>
          </div>

          <div class="location-container">
            <a-spin :spinning="locationLoading">
              <div v-if="locationOptions.length > 0" class="location-list">
                <div v-for="location in locationOptions" :key="location.id" class="location-item">
                  <div class="location-info">
                    <div class="location-details">
                      <div class="location-name">{{ location.locationName }}</div>
                      <div v-if="location.remark" class="location-remark">{{ location.remark }}</div>
                    </div>
                  </div>
                  <div class="location-actions">
                    <a-button type="text" size="small" @click="handleEditLocation(location)" class="edit-btn">
                      <EditOutlined />
                    </a-button>
                    <a-button type="text" size="small" danger @click="handleDeleteLocation(location)"
                      class="delete-btn">
                      <DeleteOutlined />
                    </a-button>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-locations">
                <p class="empty-text">No locations added yet</p>
                <p class="empty-desc">Add your first location to get started</p>
              </div>
            </a-spin>
          </div>
        </div>

        <!-- workspace location -->
        <div class="form-item">
          <label>Location <span class="required">*</span></label>
          <div class="address-selector" @click="handleSelectAddress">
            <EnvironmentOutlined class="location-icon" />
            <span class="address-text">
              {{ formData.workspaceLocation || 'select address' }}
            </span>
            <RightOutlined class="arrow-icon" />
          </div>
          <!-- 显示经纬度信息 -->
          <div v-if="formData.longitude !== null && formData.latitude !== null" class="coordinates-info">
            <small>
              经度: {{ formData.longitude?.toFixed(6) }},
              纬度: {{ formData.latitude?.toFixed(6) }}
            </small>
          </div>
        </div>



        <!-- 备注 -->
        <div class="form-item">
          <label>Remark</label>
          <a-textarea v-model:value="formData.remark" placeholder="Enter remark (optional)" :rows="3" />
        </div>
      </div>

      <!-- 成员列表 -->
      <div class="members-section" style="margin-top: 32px;">
        <label>device location members ({{ membersList.length }})</label>
        <a-spin :spinning="membersLoading">
          <div class="member-list">
            <div v-for="member in membersList" :key="member.id" class="member-item">
              <div class="member-avatar">
                <img v-if="member.memberPic" :src="member.memberPic" :alt="member.memberName" />
                <UserOutlined v-else />
              </div>
              <div class="member-info">
                <div class="member-name">{{ member.memberName }}</div>
                <div class="member-account">{{ member.memberAccount }}</div>
              </div>
              <div class="member-role">
                <a-select v-model:value="member.memberRole" style="width: 120px;"
                  @change="(value) => handleRoleChange(member, value)" :disabled="member.memberRole === '2'">
                  <a-select-option value="0">成员</a-select-option>
                  <a-select-option value="1">管理员</a-select-option>
                  <a-select-option value="2" disabled>所有者</a-select-option>
                </a-select>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!membersLoading && membersList.length === 0" class="empty-members">
              <UserOutlined class="empty-icon" />
              <p>暂无成员</p>
            </div>
          </div>
        </a-spin>

        <div class="add-member">
          <a-button type="link" @click="handleAddMember">Add Member</a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 添加邀请成员弹窗 -->
  <a-modal v-model:open="addMemberVisible" title="邀请成员" :width="400" @ok="handleConfirmAddMember"
    @cancel="handleCancelAddMember">
    <div class="add-member-form">
      <div class="form-item">
        <label>邀请方式</label>
        <!-- 邀请方式 1-邮件，2-短信 -->
        <a-select v-model:value="newMemberRole" placeholder="请选择邀请方式" style="width: 100%;">
          <a-select-option value="1">邮件</a-select-option>
          <a-select-option value="2">短信</a-select-option>
        </a-select>
      </div>

      <!-- 国家码选择（仅短信邀请时显示） -->
      <div v-if="newMemberRole === '2'" class="form-item">
        <label>国家码</label>
        <a-select v-model:value="countryCode" placeholder="请选择国家码" style="width: 100%;">
          <a-select-option value="86">+86 (中国)</a-select-option>
          <a-select-option value="1">+1 (美国/加拿大)</a-select-option>
          <a-select-option value="44">+44 (英国)</a-select-option>
          <a-select-option value="81">+81 (日本)</a-select-option>
          <a-select-option value="82">+82 (韩国)</a-select-option>
          <a-select-option value="65">+65 (新加坡)</a-select-option>
          <a-select-option value="852">+852 (香港)</a-select-option>
          <a-select-option value="853">+853 (澳门)</a-select-option>
          <a-select-option value="886">+886 (台湾)</a-select-option>
        </a-select>
      </div>

      <div class="form-item">
        <label>{{ newMemberRole === '1' ? '邮箱地址' : '手机号码' }}</label>
        <a-input v-model:value="newMemberEmail" :placeholder="newMemberRole === '1' ? '请输入邮箱地址' : '请输入手机号码'" />
      </div>
    </div>
  </a-modal>

  <!-- 地址选择弹窗 - 包含Google地图 -->
  <a-modal v-model:open="addressModalVisible" title="Select Address" :width="800" :height="600"
    @ok="handleConfirmAddress" @cancel="handleCancelAddress">
    <div class="address-form">
      <!-- 地址搜索框 -->
      <div class="search-section">
        <a-input v-model:value="searchAddress" placeholder="搜索地址或点击地图选择位置" @keyup.enter="handleSearchAddress" />
        <a-button type="primary" @click="handleSearchAddress" style="margin-left: 8px;">
          搜索
        </a-button>
      </div>

      <!-- Google地图容器 -->
      <div ref="mapContainer" class="map-container"></div>

      <!-- 选中的地址信息 -->
      <div v-if="selectedLocationInfo" class="selected-info">
        <h4>选中位置信息:</h4>
        <p><strong>地址:</strong> {{ selectedLocationInfo.address }}</p>
        <p><strong>经度:</strong> {{ selectedLocationInfo.lng?.toFixed(6) }}</p>
        <p><strong>纬度:</strong> {{ selectedLocationInfo.lat?.toFixed(6) }}</p>
      </div>
    </div>
  </a-modal>

  <!-- 添加其他位置弹窗 -->
  <a-modal v-model:open="addLocationModalVisible" :title="editingLocation ? 'Edit Location' : 'Add Other Location'"
    :width="400" @ok="handleConfirmAddLocation" @cancel="handleCancelAddLocation">
    <div class="add-location-form">
      <a-input v-model:value="newLocationName" placeholder="Enter location name" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import {
  LeftOutlined,
  RightOutlined,
  UserOutlined,
  EnvironmentOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import {
  saveOrUpdate,
  generateInviteCode,
  aglWorkspaceMemberList,
  aglDeviceLocationList,
  aglDeviceLocationSaveOrUpdate,
  aglDeviceLocationDelete,
  editWorkspaceMember
} from '/@/views/aglWorkspace/AglWorkspace.api';
import {
  loadGoogleMaps,
  createMap,
  createGeocoder,
  createPlacesService,
  createMarker,
  getGoogleMaps
} from '/@/utils/googleMaps';

// Props 定义
interface Props {
  visible: boolean;
  locationData: {
    name: string;
    locationManagement: string;
    location: string;
    roomCount: number;
    members: Array<{
      id: string;
      name: string;
      role: string;
      avatar?: string;
    }>;
  };
}

const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'update': [locationData: any];
}>();

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 编辑表单数据
const formData = ref({
  id: '',
  workspaceName: '',
  workspaceLocation: '',
  longitude: null as number | null,
  latitude: null as number | null,
  remark: ''
});

const locationOptions = ref<Array<{
  id: string;
  locationName: string;
  workspaceId: string;
  createTime: string;
  remark?: string;
}>>([]);
const locationLoading = ref(false);

// 成员列表数据
const membersList = ref<Array<{
  id: string;
  createBy: string;
  createTime: string;
  updateBy: string | null;
  updateTime: string | null;
  sysOrgCode: string;
  memberName: string;
  memberRole: string;
  memberAccount: string;
  memberPic: string;
  workspaceId: string;
  inviteRecordId: string | null;
  memberRole_dictText: string;
}>>([]);
const membersLoading = ref(false);

const addMemberVisible = ref(false);
const newMemberEmail = ref('');
const newMemberRole = ref('1'); // 默认邮件邀请
const countryCode = ref('86'); // 默认中国区号
const saveLoading = ref(false);
const addressModalVisible = ref(false);
const addLocationModalVisible = ref(false);
const newLocationName = ref('');
const searchAddress = ref('');
const selectedLocationInfo = ref<{
  address: string;
  lat: number;
  lng: number;
} | null>(null);

// Google地图相关数据
const mapContainer = ref<HTMLDivElement | null>(null);

let googleMap: any = null;
let currentMarker: any = null;
let geocoder: any = null;
let placesService: any = null;

// 计算属性

// 方法定义
const handleCancel = () => {
  modalVisible.value = false;
};

// 加载成员列表
let loadingPromise: Promise<void> | null = null;
const loadMembersList = async () => {
  if (!formData.value.id) {
    console.log('工作空间ID为空，无法加载成员列表');
    return;
  }

  // 如果正在加载中，返回当前的加载Promise，避免重复请求
  if (loadingPromise) {
    return loadingPromise;
  }

  try {
    membersLoading.value = true;

    loadingPromise = (async () => {
      const response = await aglWorkspaceMemberList({
        workspaceId: formData.value.id
      });

      if (response && response.records) {
        membersList.value = response.records;
        console.log('成员列表加载成功:', response.records);
      } else {
        membersList.value = [];
      }
    })();

    await loadingPromise;
  } catch (error) {
    console.error('加载成员列表失败:', error);
    membersList.value = [];
  } finally {
    membersLoading.value = false;
    loadingPromise = null;
  }
};

const handleAddMember = () => {
  addMemberVisible.value = true;
};

const handleCancelAddMember = () => {
  addMemberVisible.value = false;
  newMemberEmail.value = '';
  newMemberRole.value = '1';
  countryCode.value = '86';
};

const handleConfirmAddMember = async () => {
  if (!newMemberEmail.value) {
    message.error('请输入邮箱地址或手机号码');
    return;
  }

  if (!newMemberRole.value) {
    message.error('请选择邀请方式');
    return;
  }

  try {
    // 准备邀请码生成参数
    const params: any = {
      beInvited: newMemberEmail.value.trim(),
      workspaceId: formData.value.id || (props.locationData as any).id || '',
      inviteType: newMemberRole.value, // 1-邮件，2-短信
    };

    // 根据邀请方式设置对应参数
    if (newMemberRole.value === '1') {
      // 邮件邀请
      params.inviteEmail = newMemberEmail.value.trim();
    } else if (newMemberRole.value === '2') {
      // 短信邀请
      params.invitePhone = newMemberEmail.value.trim();
      params.countryCode = countryCode.value;
    }

    console.log('生成邀请码参数:', params);

    // 调用生成邀请码API
    const result = await generateInviteCode(params);

    if (result) {
      const inviteTypeText = newMemberRole.value === '1' ? '邮件' : '短信';
      message.success(`${inviteTypeText}邀请发送成功！`);

      // 关闭弹窗并重置表单
      addMemberVisible.value = false;
      newMemberEmail.value = '';
      newMemberRole.value = '1';
      countryCode.value = '86';

      // 可以在这里处理返回的邀请码或其他逻辑
      console.log('邀请码生成结果:', result);
    }

  } catch (error) {
    console.error('生成邀请码失败:', error);
    message.error('邀请发送失败，请重试');
  }
};

// 处理成员角色修改
const handleRoleChange = async (member: any, newRole: string) => {
  // 所有者角色不能被修改
  if (member.memberRole === '2') {
    message.warning('所有者角色不能被修改');
    return;
  }

  try {
    // 调用修改成员角色的API
    await editWorkspaceMember({
      id: member.id,
      memberRole: newRole === '1' ? '1' : '0' // 管理员-1,成员-0
    });

    // 更新本地数据
    member.memberRole = newRole;
    member.memberRole_dictText = newRole === '1' ? '管理员' : '成员';

    message.success('角色修改成功');
  } catch (error) {
    console.error('修改角色失败:', error);
    message.error('角色修改失败，请重试');

    // 恢复原来的值
    member.memberRole = member.memberRole;
  }
};


// 编辑功能相关方法
const handleSave = async () => {
  // 表单验证
  if (!formData.value.workspaceName.trim()) {
    message.error('请输入工作空间名称');
    return;
  }

  if (!formData.value.workspaceLocation.trim()) {
    message.error('请选择工作空间地址');
    return;
  }

  try {
    saveLoading.value = true;

    // 准备API请求数据
    const requestData = {
      id: formData.value.id,
      workspaceName: formData.value.workspaceName.trim(),
      workspaceLocation: formData.value.workspaceLocation.trim(),
      longitude: formData.value.longitude || 0,
      latitude: formData.value.latitude || 0,
      remark: formData.value.remark?.trim() || ''
    };

    console.log('更新工作空间数据:', requestData);

    // 调用编辑API接口（第二个参数为true表示编辑模式）
    await saveOrUpdate(requestData, true);

    // 触发更新事件
    emit('update', requestData);

    // 关闭弹窗
    modalVisible.value = false;

  } catch (error) {
    console.error('更新工作空间失败:', error);
    message.error('更新失败，请检查网络连接或联系管理员');
  } finally {
    saveLoading.value = false;
  }
};

const handleSelectAddress = () => {
  addressModalVisible.value = true;
  searchAddress.value = formData.value.workspaceLocation || '';
  selectedLocationInfo.value = null;
};

const handleAddOtherLocation = () => {
  addLocationModalVisible.value = true;
};

const handleConfirmAddress = () => {
  if (selectedLocationInfo.value) {
    formData.value.workspaceLocation = selectedLocationInfo.value.address;
    formData.value.longitude = selectedLocationInfo.value.lng;
    formData.value.latitude = selectedLocationInfo.value.lat;
    message.success('地址选择成功');
    addressModalVisible.value = false;
  } else {
    message.warning('请选择一个位置');
  }
};

const handleCancelAddress = () => {
  addressModalVisible.value = false;
  selectedLocationInfo.value = null;
  searchAddress.value = '';
};



// 加载location列表
const loadLocationList = async () => {
  if (!formData.value.id) {
    console.log('工作空间ID为空，无法加载位置列表');
    return;
  }

  try {
    locationLoading.value = true;
    const response = await aglDeviceLocationList({
      workspaceId: formData.value.id
    });

    if (response && response.records) {
      locationOptions.value = response.records;
      console.log('位置列表加载成功:', response.records);
    } else {
      locationOptions.value = [];
    }
  } catch (error) {
    console.error('加载位置列表失败:', error);
    locationOptions.value = [];
  } finally {
    locationLoading.value = false;
  }
};

// 编辑位置
const editingLocation = ref<any>(null);
const handleEditLocation = (location: any) => {
  editingLocation.value = location;
  newLocationName.value = location.locationName;
  addLocationModalVisible.value = true;
};

// 删除位置
const handleDeleteLocation = async (location: any) => {
  try {
    await aglDeviceLocationDelete({ id: location.id }, () => {
      loadLocationList(); // 重新加载列表
    });
  } catch (error) {
    console.error('删除位置失败:', error);
    message.error('删除失败，请重试');
  }
};

const handleConfirmAddLocation = async () => {
  if (!newLocationName.value.trim()) {
    message.error('Please enter location name');
    return;
  }

  try {
    const params = {
      workspaceId: formData.value.id,
      locationName: newLocationName.value.trim()
    };

    if (editingLocation.value) {
      // 编辑模式
      await aglDeviceLocationSaveOrUpdate({
        ...params,
        id: editingLocation.value.id
      }, true);
    } else {
      // 添加模式
      await aglDeviceLocationSaveOrUpdate(params, false);
    }

    addLocationModalVisible.value = false;
    newLocationName.value = '';
    editingLocation.value = null;
    loadLocationList(); // 重新加载列表
  } catch (error) {
    console.error('保存位置失败:', error);
    message.error('保存失败，请重试');
  }
};

const handleCancelAddLocation = () => {
  addLocationModalVisible.value = false;
  newLocationName.value = '';
  editingLocation.value = null;
};

// Google地图相关方法
// 初始化Google地图
const initGoogleMap = async () => {
  try {
    await loadGoogleMaps();
    await nextTick();

    if (!mapContainer.value) return;

    // 初始化地图（默认定位到北京）
    const defaultLocation = { lat: 39.9042, lng: 116.4074 };

    googleMap = createMap(mapContainer.value, {
      zoom: 15,
      center: defaultLocation,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
    });

    // 初始化地理编码器
    geocoder = createGeocoder();

    // 初始化Places服务
    placesService = createPlacesService(googleMap);

    // 添加地图点击事件
    const googleMaps = getGoogleMaps();
    googleMaps.event.addListener(googleMap, 'click', (event: any) => {
      handleMapClick(event.latLng);
    });

    // 尝试获取用户当前位置
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          googleMap.setCenter(userLocation);
          googleMap.setZoom(15);
        },
        () => {
          console.log('无法获取用户位置，使用默认位置');
        }
      );
    }

  } catch (error) {
    console.error('初始化Google地图失败:', error);
    message.warning('Google地图初始化失败，请检查网络连接或API配置');
  }
};

// 处理地图点击事件
const handleMapClick = (latLng: any) => {
  const lat = latLng.lat();
  const lng = latLng.lng();

  // 移除之前的标记
  if (currentMarker) {
    currentMarker.setMap(null);
  }

  // 添加新标记
  currentMarker = createMarker({
    position: { lat, lng },
    map: googleMap,
    title: '选中位置',
  });

  // 反向地理编码获取地址
  geocoder.geocode({ location: { lat, lng } }, (results: any, status: any) => {
    if (status === 'OK' && results[0]) {
      const address = results[0].formatted_address;
      selectedLocationInfo.value = {
        address,
        lat,
        lng,
      };
    } else {
      selectedLocationInfo.value = {
        address: `位置: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
        lat,
        lng,
      };
    }
  });
};

// 重写地址搜索方法以支持Google Maps
const handleSearchAddress = () => {
  if (!searchAddress.value.trim()) {
    message.warning('请输入要搜索的地址');
    return;
  }

  const googleMaps = getGoogleMaps();
  if (!googleMaps || !placesService) {
    message.error('Google Maps服务不可用，请检查网络连接');
    return;
  }

  const request = {
    query: searchAddress.value,
    fields: ['name', 'geometry', 'formatted_address'],
  };

  placesService.findPlaceFromQuery(request, (results: any, status: any) => {
    if (status === googleMaps.places.PlacesServiceStatus.OK && results[0]) {
      const place = results[0];
      const location = place.geometry.location;

      // 移动地图到搜索结果
      googleMap.setCenter(location);
      googleMap.setZoom(15);

      // 模拟点击事件
      handleMapClick(location);
    } else {
      message.error('未找到该地址，请尝试其他关键词');
    }
  });
};

// 监听locationData变化，初始化表单数据
watch(() => props.locationData, (newData) => {
  if (newData) {
    formData.value = {
      id: (newData as any).id || '',
      workspaceName: newData.name || '',
      workspaceLocation: newData.location || '',
      longitude: (newData as any).longitude || null,
      latitude: (newData as any).latitude || null,
      remark: (newData as any).remark || ''
    };
  }
}, { immediate: true });

// 监听弹窗显示状态，在打开时加载成员列表和位置列表
watch(() => props.visible, (newValue) => {
  if (newValue && formData.value.id) {
    loadMembersList();
    loadLocationList();
  }
});

// 监听地址选择弹窗的打开状态
watch(addressModalVisible, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initGoogleMap();
    });
  }
});
</script>

<style scoped lang="less">
.info-section {
  margin-bottom: 32px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  label {
    font-weight: 500;
    color: #333;
  }

  .value {
    display: flex;
    align-items: center;
    color: #666;
    cursor: pointer;

    &:hover {
      color: #1890ff;
    }
  }

  .arrow-icon {
    margin-left: 8px;
    font-size: 12px;
    color: #ccc;
  }
}


.member-list {
  margin-bottom: 16px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 12px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.member-account {
  font-size: 12px;
  color: #999;
}

.member-role {
  color: #666;
  font-size: 14px;
  margin-right: 12px;
}

.empty-members {
  text-align: center;
  padding: 40px 20px;
  color: #999;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.add-member {
  text-align: left;
}

.add-member-form {
  padding: 20px 0;
}

/* 编辑表单样式 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-item,
.members-section {
  display: flex;
  flex-direction: column;

  label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;

    .required {
      color: #ff4d4f;
      margin-left: 4px;
    }
  }
}

.address-selector {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #1890ff;
  }
}

.location-icon {
  color: #ff4d4f;
  margin-right: 8px;
  font-size: 16px;
}

.address-text {
  flex: 1;
  color: #666;
}

.coordinates-info {
  margin-top: 4px;
  color: #999;
  font-size: 12px;
}

.location-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  label {
    margin: 0;
  }

}

.location-container {
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.location-list {
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.location-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
  word-break: break-all;
  min-width: 0;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);

    .location-actions {
      opacity: 1;
    }
  }
}

.location-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;

  .location-icon {
    color: #1890ff;
    font-size: 16px;
    margin-top: 2px;
  }

  .location-details {
    flex: 1;

    .location-name {
      color: #333;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 2px;
    }

    .location-remark {
      color: #666;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 4px;
    }
  }
}

.location-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-top: 2px;
  flex-shrink: 0;

  .edit-btn {
    color: #1890ff;

    &:hover {
      background: #e6f7ff;
    }
  }

  .delete-btn {
    color: #ff4d4f;

    &:hover {
      background: #fff2f0;
    }
  }
}

.empty-locations {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .empty-text {
    color: #666;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  .empty-desc {
    color: #999;
    font-size: 14px;
    margin: 0;
  }
}

.add-location-section {
  .add-location-btn {
    height: 30px;
    border-style: dashed;
    font-weight: 500;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
}

.address-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-section {
  display: flex;
  align-items: center;
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #f5f5f5;
}

.selected-info {
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;

  h4 {
    margin: 0 0 8px 0;
    color: #52c41a;
    font-size: 14px;
  }

  p {
    margin: 4px 0;
    font-size: 12px;
    color: #333;
  }
}

.add-location-form {
  padding: 20px 0;
}

.add-member-form {
  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
      font-size: 14px;
      display: block;
    }
  }
}
</style>
