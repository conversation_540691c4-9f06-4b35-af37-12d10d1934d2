import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';

//列表数据
export const columns: BasicColumn[] = [
   {
    title: '经销商',
    align:"center",
    sorter: true,
    dataIndex: 'saler_dictText'
   },
   {
    title: '模块SN',
    align:"center",
    sorter: true,
    dataIndex: 'moduleSn'
   },
   {
    title: '机箱SN',
    align:"center",
    sorter: true,
    dataIndex: 'enclosureSn'
   },
   {
    title: '绑定人',
    align:"center",
    sorter: true,
    dataIndex: 'createBy_dictText',
    auth:"aglProvider:agl_bind_modulesn:add"
   },
   {
    title: '绑定时间',
    align:"center",
    sorter: true,
    dataIndex: 'createTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "经销商",
      field: 'saler',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"agl_provider,provider_name,id"
      },
      //colProps: {span: 6},
 	},
	{
      label: "模块SN",
      field: 'moduleSn',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "机箱SN",
      field: 'enclosureSn',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "绑定人",
      field: 'createBy',
      component: 'JSelectUser',
      componentProps:{
      },
      auth:"aglProvider:agl_bind_modulesn:add"
      //colProps: {span: 6},
 	},
	{
      label: "绑定时间",
      field: 'createTime',
      component: 'RangePicker',
      componentProps: {
         showTime: true,
         valueFormat: 'YYYY-MM-DD HH:mm:ss',
         placeholder: ['开始时间', '结束时间']
       },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '经销商',
    field: 'saler',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"agl_provider,provider_name,id"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入经销商!'},
          ];
     },
  },
  {
    label: '模块SN',
    field: 'moduleSn',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入模块SN!'},
          ];
     },
  },
  {
    label: '机箱SN',
    field: 'enclosureSn',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入机箱SN!'},
          ];
     },
  },
  {
    label: '绑定人',
    field: 'createBy',
    component: 'JSelectUser',
    componentProps:{
    },
  },
  {
    label: '绑定时间',
    field: 'createTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  saler: {title: '经销商',order: 0,view: 'list', type: 'string',dictTable: "agl_provider", dictCode: 'id', dictText: 'provider_name',},
  moduleSn: {title: '模块SN',order: 1,view: 'text', type: 'string',},
  enclosureSn: {title: '机箱SN',order: 2,view: 'text', type: 'string',},
  createBy: {title: '绑定人',order: 3,view: 'sel_user', type: 'string',},
  createTime: {title: '绑定时间',order: 4,view: 'datetime-range', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}