<!--
  用户访问控制页面
  功能：用户信息管理、设备位置管理、群组管理等
  作者：开发团队
  创建时间：2024
-->
<template>
    <div class="page-container">
        <h1 class="page-title">{{ t('routes.airgle.userAccessControl') }}</h1>
        <div class="user-access-control">

            <div class="content-wrapper">
                <!-- 左侧内容区域 -->
                <div class="left-content">
                    <!-- 用户信息卡片 -->
                    <div class="user-card" @click="showPersonalInfo">
                        <div class="user-avatar">
                            <UserOutlined />
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ userInfo.nickname }}</div>
                            <div class="user-role">{{ userInfo.role }}</div>
                        </div>
                        <RightOutlined class="arrow-icon" />
                    </div>


                    <!-- 设备位置管理区域 -->
                    <div class="device-location-card">
                        <div class="card-header">
                            <HomeOutlined class="location-icon" />
                            <h3>Device Location Management</h3>
                        </div>
                        <div class="device-location-content">
                            <!-- 操作按钮 -->
                            <div class="location-actions">
                                <a-button type="default" @click="showAddDeviceLocation">
                                    <PlusOutlined />
                                    Add device location
                                </a-button>

                                <a-button type="default" @click="showJoinGroup">
                                    <TeamOutlined />
                                    Join a group
                                </a-button>
                            </div>

                            <!-- 设备位置列表 -->
                            <div class="location-list-section">
                                <h4>Device Locations ({{ deviceLocations.length }})</h4>
                                <div class="location-list">
                                    <div v-for="location in deviceLocations" :key="location.id" class="location-item">
                                        <div class="location-info">
                                            <div class="location-name">{{ location.name }}</div>
                                            <div class="location-details">
                                                <span class="location-address">{{ location.address }}</span>
                                            </div>
                                        </div>
                                        <div class="location-actions-right">
                                            <a-button type="text" size="small" @click="editLocation(location)">
                                                <EditOutlined />
                                            </a-button>
                                            <a-popconfirm title="Are you sure you want to delete this location?"
                                                ok-text="Yes" cancel-text="No" @confirm="deleteLocation(location.id)">
                                                <a-button type="text" size="small" danger>
                                                    <DeleteOutlined />
                                                </a-button>
                                            </a-popconfirm>
                                        </div>
                                    </div>

                                    <!-- 空状态 -->
                                    <div v-if="deviceLocations.length === 0" class="empty-locations">
                                        <HomeOutlined class="empty-icon" />
                                        <p>No device locations yet</p>
                                        <a-button type="primary" @click="showAddDeviceLocation">
                                            <PlusOutlined />
                                            Add your first location
                                        </a-button>
                                    </div>
                                </div>

                                <!-- 简单分页组件 -->
                                <div v-if="pagination.total > pagination.pageSize" class="pagination-wrapper">
                                    <button class="pagination-btn" @click="prevPage" :disabled="pagination.current <= 1">
                                        上一页
                                    </button>

                                    <span class="pagination-info">
                                        第 {{ pagination.current }} 页 / 共 {{ Math.ceil(pagination.total /
                                        pagination.pageSize) }} 页
                                    </span>

                                    <button class="pagination-btn" @click="nextPage"
                                        :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)">
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="right-content">
                    <!-- 消息中心 -->
                    <MessageCenter />
                </div>
            </div>

            <!-- 个人信息弹窗 -->
            <PersonalInfoModal v-model:visible="personalInfoVisible" :user-info="userInfo" @update="handleUpdateUserInfo" />

            <!-- 设备位置设置弹窗 -->
            <DeviceLocationModal v-model:visible="deviceLocationVisible" :location-data="locationData"
                @update="handleWorkspaceSuccess" />

            <!-- 添加设备位置弹窗 -->
            <AddLocationModal v-model:visible="addLocationVisible" @add="handleWorkspaceSuccess"
                @success="handleWorkspaceSuccess" />

            <!-- 加入群组弹窗 -->
            <JoinGroupModal v-model:visible="joinGroupVisible" @join="handleJoinGroup" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
    UserOutlined,
    HomeOutlined,
    RightOutlined,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    TeamOutlined
} from '@ant-design/icons-vue';

// 导入子组件
import PersonalInfoModal from './components/PersonalInfoModal.vue';
import DeviceLocationModal from './components/DeviceLocationModal.vue';
import AddLocationModal from './components/AddLocationModal.vue';
import JoinGroupModal from './components/JoinGroupModal.vue';
import MessageCenter from './components/MessageCenter.vue';

// 导入类型定义和API
import type { UserInfo, LocationData, AddLocationFormData } from './types';
import {
    updateUserInfo,
    joinGroup,
    getWorkspaceList,
    type WorkspaceRecord
} from './api';
// 导入工作空间删除接口
import { deleteOne } from '/@/views/aglWorkspace/AglWorkspace.api';
import { useI18n } from '/@/hooks/web/useI18n'

const { t } = useI18n()
// 响应式数据
const userInfo = ref<UserInfo>({
    nickname: 'Airgle - SZ',
    role: 'Admin',
    timezone: 'Shanghai'
});

const locationData = ref<LocationData>({
    name: 'Airgle-SZ',
    locationManagement: '7 Room(s)',
    location: 'Florida',
    roomCount: 7,
    members: [
        {
            id: '1',
            name: 'Airgle-SZ',
            role: 'Admin',
            avatar: ''
        }
    ]
});

// 设备位置列表数据
const deviceLocations = ref<Array<{
    id: string;
    name: string;
    address: string;
    roomCount: number;
    memberCount: number;
}>>([]);

// 编辑位置数据
const editLocationData = ref<{
    id: string;
    workspaceName: string;
    workspaceLocation: string;
    longitude?: number;
    latitude?: number;
    remark?: string;
} | null>(null);

// 分页相关数据
const pagination = ref({
    current: 1,
    pageSize: 5,
    total: 0
});

// 弹窗显示状态
const personalInfoVisible = ref(false);
const deviceLocationVisible = ref(false);
const addLocationVisible = ref(false);
const joinGroupVisible = ref(false);

// 方法定义
const showPersonalInfo = () => {
    personalInfoVisible.value = true;
};



const showAddDeviceLocation = () => {
    // 清空编辑数据，确保是添加模式
    editLocationData.value = null;
    addLocationVisible.value = true;
};

const showJoinGroup = () => {
    joinGroupVisible.value = true;
};

const handleUpdateUserInfo = async (newUserInfo: Partial<UserInfo>) => {
    try {
        const updatedUser = await updateUserInfo(newUserInfo);
        userInfo.value = { ...userInfo.value, ...updatedUser };
        console.log('User info updated:', updatedUser);
    } catch (error) {
        console.error('Failed to update user info:', error);
    }
};

// 统一处理工作空间操作成功（添加/更新）
const handleWorkspaceSuccess = async () => {
    try {
        // 清空编辑数据
        editLocationData.value = null;

        // 关闭编辑弹窗
        deviceLocationVisible.value = false;

        // 重新加载工作空间列表
        await loadWorkspaceList();
    } catch (error) {
        console.error('Failed to reload workspace list:', error);
        message.error('刷新工作空间列表失败');
    }
};

const handleJoinGroup = async (invitationCode: string) => {
    try {
        const groupInfo = await joinGroup(invitationCode);
        console.log('Joined group:', groupInfo);
    } catch (error) {
        console.error('Failed to join group:', error);
    }
};

// 编辑位置
const editLocation = (location: any) => {
    // 将编辑数据转换为DeviceLocationModal需要的格式，包含所有编辑需要的字段
    locationData.value = {
        name: location.name,
        locationManagement: `${location.roomCount} Room(s)`,
        location: location.address,
        roomCount: location.roomCount,
        members: [
            {
                id: '1',
                name: location.name,
                role: 'Admin',
                avatar: ''
            }
        ],
        // 添加编辑所需的额外字段
        id: location.id,
        longitude: location.longitude || null,
        latitude: location.latitude || null,
        remark: location.remark || ''
    } as any;

    // 打开DeviceLocationModal进行编辑
    deviceLocationVisible.value = true;
};

// 删除位置
const deleteLocation = async (locationId: string) => {
    try {
        const locationToDelete = deviceLocations.value.find(loc => loc.id === locationId);
        if (!locationToDelete) {
            message.error('位置不存在');
            return;
        }


        // 调用工作空间删除接口
        await deleteOne({ id: locationId }, () => {
            console.log('删除成功回调执行');
        });

        // 重新加载工作空间列表
        await loadWorkspaceList();
        message.success(`位置 "${locationToDelete.name}" 删除成功`);

    } catch (error) {
        console.error('Failed to delete location:', error);
    }
};

// 上一页
const prevPage = async () => {
    if (pagination.value.current > 1) {
        pagination.value.current--;
        await loadWorkspaceList();
    }
};

// 下一页
const nextPage = async () => {
    const totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize);
    if (pagination.value.current < totalPages) {
        pagination.value.current++;
        await loadWorkspaceList();
    }
};

// 获取工作空间列表
const loadWorkspaceList = async (page?: number, pageSize?: number) => {
    try {
        const currentPage = page || pagination.value.current;
        const currentPageSize = pageSize || pagination.value.pageSize;

        const response = await getWorkspaceList({
            pageNo: currentPage,
            pageSize: currentPageSize
        });

        if (response && response.records) {
            // 将工作空间数据映射到设备位置数据格式
            deviceLocations.value = response.records.map((workspace: WorkspaceRecord) => ({
                id: workspace.id,
                name: workspace.workspaceName,
                address: workspace.workspaceLocation,
                roomCount: 1, // 默认值，可根据实际情况调整
                memberCount: 1 // 默认值，可根据实际情况调整
            }));

            // 更新分页信息
            pagination.value.current = currentPage;
            pagination.value.pageSize = currentPageSize;
            pagination.value.total = response.total || 0;
        }
    } catch (error) {
        console.error('Failed to load workspace list:', error);
        message.error('获取工作空间列表失败');
    }
};

// 生命周期
onMounted(async () => {
    console.log('User Access Control page mounted');
    await loadWorkspaceList();
});
</script>

<style scoped lang="less">
  /* 页面容器 */
  .page-container {
    padding: 0px 24px;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
  }

.user-access-control {
    background-color: #f5f5f5;
    height: 90vh; /* 设置整个页面高度为视口高度 */
    overflow: hidden; /* 防止页面滚动 */

    .content-wrapper {
        display: flex;
        gap: 24px;

        .left-content,
        .right-content {
            flex: 1;
            width: 50%;
        }
    }
}

.page-title {
    font-size: 32px;
    font-weight: bold;
    color: #000;
    margin-bottom: 32px;
}

.left-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: calc(100vh - 200px);
}

.user-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #1890ff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-right: 16px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    margin-bottom: 4px;
}

.user-role {
    font-size: 14px;
    color: #666;
}

.arrow-icon {
    color: #ccc;
    font-size: 16px;
}

.device-location-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 68vh; /* 固定高度，减去用户卡片、标题和padding的高度 */
    overflow: hidden; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.location-icon {
    font-size: 48px;
    color: #1890ff;
    margin-right: 16px;
}

.card-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #000;
    margin: 0;
}

.location-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;

    .ant-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: flex-start;
        height: 40px;
    }
}

.location-list-section {
    h4 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
    }
}

.location-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 30vh;
    overflow-y: auto;
}

.location-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.2s;

    &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

.location-info {
    flex: 1;
}

.location-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.location-details {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #666;
}

.location-address {
    &::before {
        content: "📍 ";
    }
}

.location-rooms {
    &::before {
        content: "🏠 ";
    }
}

.location-actions-right {
    display: flex;
    gap: 8px;
}

.empty-locations {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        color: #d9d9d9;
    }

    p {
        font-size: 16px;
        margin-bottom: 16px;
    }
}

.pagination-wrapper {
    margin-top: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    gap: 16px;
}

.pagination-btn {
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #ffffff;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
        border-color: #1890ff;
        color: #1890ff;
    }

    &:disabled {
        color: #bfbfbf;
        cursor: not-allowed;
        background: #f5f5f5;
    }
}

.pagination-info {
    color: #666;
    font-size: 14px;
    margin: 0 8px;
}

.right-content {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: calc(100vh - 200px); /* 与左侧卡片保持一致的高度 */
}



.message-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-messages {
    text-align: center;
    color: #ccc;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.no-messages p {
    font-size: 16px;
    margin: 0;
}

// 响应式设计
@media (max-width: 768px) {
    .content-wrapper {
        grid-template-columns: 1fr;
    }
}
</style>
