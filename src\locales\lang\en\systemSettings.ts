export default {
  persona: 'Persona Information',
  account: 'Account and Security',
  device: 'Device Update',
  touchTone: 'Touch Tone on Pane',
  notification: 'System Alert',
  temperature: 'Temperature Unit',
  debug: 'Multilingual Debug Mode',
  features: 'More Features',
  about: 'About',
  privacy: 'Privacy Settings',
  policy: 'Privacy Policy Management',
  network: 'Network Diagnosis',
  cache: 'Clear Cache',
  thirdPartyApp: 'Third Party App',
  accountSecurity: 'Account Security',
  personal: 'Personal Information',
  // Third-party binding related
  accountBinding: 'Account Binding',
  qqBinding: 'QQ Binding',
  appleBinding: 'Apple Binding',
  wechatBinding: 'WeChat Binding',
  dingtalkBinding: 'DingTalk Binding',
  googleBinding: 'Google Binding',
  enterpriseWechatBinding: 'Enterprise WeChat Binding',
  // Binding status
  bound: 'Bound',
  unbound: 'Unbound',
  bind: 'Bind',
  unbind: 'Unbind',
  // Binding prompts
  bindSuccess: 'Binding successful',
  unbindSuccess: 'Unbinding successful',
  bindFailed: 'Binding failed',
  unbindFailed: 'Unbinding failed',
  confirmUnbind: 'Are you sure you want to unbind?',
  // Third-party platform names
  qq: 'QQ',
  apple: 'Apple',
  wechat: 'WeChat',
  dingtalk: 'DingTalk',
  google: 'Google',
  enterpriseWechat: 'Enterprise WeChat',
  // Account settings related
  accountTitle: 'Account',
  phone: 'Phone',
  email: 'Email',
  password: 'Password',
  modify: 'Modify',
  notFilled: 'Not filled',
  accountCancellation: 'Account Cancellation',
  cancel: 'Cancel',
  verify: 'Verify',
  // Base settings related
  detailedInfo: 'Detailed Information',
  contactInfo: 'Contact Information',
  birthday: 'Birthday',
  gender: 'Gender',
  position: 'Position',
  edit: 'Edit',
  editName: 'Edit Name',
  pleaseEnterName: 'Please enter your name',
  usageTime: 'Usage',
  days: 'days',
  male: 'Male',
  female: 'Female',
}; 