export default {
  group: {
    groupName: '群组名称',
    groupSpace: '所属空间',
    remark: '备注',
    title: '群组',
    workspace_name: '工作空间名称',
    workspace_location: '工作空间位置',
    groupSpaceSelect: '请选择群组空间',
    detail: '群组详情',
    edit: '编辑群组',
    add: '新增群组',
    iconUrl: '图片信息',
    username: '系统账号',
    realname: '用户名称',
    sex: '性别',
    email: '邮箱',
    phone: '手机号码',
    birthday: '生日',
    status: '用户状态',
    groupDeviceSelect: '请选择设备',
  },
  userinfo: {
    title: '用户信息',
    detail: '用户详情',
    edit: '编辑用户',
    add: '添加用户',
    username: '系统账号',
    realname: '用户名称',
    sex: '性别',
    email: '邮箱',
    phone: '手机号码',
    birthday: '生日',
    status: '用户状态',
    userType: '用户类别',
    addOldUser: '可添加旧用户',
    userSource: '用户来源',
    remark: '备注',

    approveRefuse: '审批拒绝',
    approvePass: '审批通过',
    unApprove: '撤回审批',
    approve: '审批',
    ListExists: '已存在相同的用户信息!',
    approveRefuseSuccess: '审批拒绝成功',
    approvePassSuccess: '审批通过成功',
    unApproveSuccess: '撤回审批成功',

    approveRefuseError: '审批拒绝失败',
    approvePassError: '审批通过失败',
    unApproveError: '撤回审批失败',

    approveSelect: '请选择要审批的数据',

  },
  workspace: {
    title: '工作空间',
    detail: '工作空间详情',
    edit: '编辑工作空间',
    add: '新增工作空间',
    workspaceName: '名称',
    workspaceLocation: '位置',

    locationDetail: '设备位置详情',
    locationEdit: '编辑设备位置',
    locationAdd: '新增设备位置',

    memberDetail: '空间成员详情',
    memberEdit: '编辑空间成员',
    memberAdd: '新增空间成员',

    inviteDetail: '邀请记录详情',
    inviteEdit: '编辑邀请记录',
    inviteAdd: '新增邀请记录',

    remark: '备注',
    deviceLocation: '设备位置',
    workspaceMembers: '空间成员',
    inviteRecords: '邀请记录',
    editSuccess: '编辑工作空间成功',
    deleteSuccess: '删除工作空间成功',
    deleteError: '删除工作空间失败',
    deleteSelect: '请选择要删除的数据',
    longitude: '经度',
    latitude: '纬度',
    memberRole: '成员角色',
    inviteTime: '邀请时间',
    joinTime: '加入时间',
    beInvited: '被邀请人',
    inviteType: '邀请方式',
    invitePhone: '手机号码',
    inviteEmail: '邮箱',
    memberAccount: '关联账号',
    memberPic: '成员头像',
    confirmDelete: '是否确认删除？删除后，所有成员会被删除、数据会被清空、已添加设备会被解绑和重置'
  },
  common: {
    sort: '排序',
    all: '全部',
    online: '在线',
    offline: '离线',
    yes: '是',
    no: '否'
  },
  device: {
    deviceModel: '设备管理',
    deviceModelAdd: '新增设备',
    searchPlaceholder: '搜索设备',
    deviceCustomize: '自定义',

    outdoor: '室外',
    indoor: '室内',

    manual: '手动模式',
    auto: '自动模式',
    sleep: '休眠模式',

    environment: '环境',
    connection: '连接状态',
    data_publication: '数据发布',
    mode: '模式',
    pleaseSelect: '请选择设备',
    addSuccess: '设备添加成功',
    filter: {
      environment: '环境',
      connection: '连接状态',
      data_publication: '数据发布',
      mode: '模式',
      outdoor: '室外',
      indoor: '室内',
      manual: '手动',
      auto: '自动',
      sleep: '睡眠'
    }
  },
  groupDevice: {
    deviceModel: '组管理',
    createGroup: '创建组',
    searchPlaceholder: '搜索组',
    pleaseSelectGroup: '请选择一个组',
    pleaseSelectDevice: '请选择设备查看图表',
  },
  msgCategory:{
    deviceLocation:'设备位置',
    notice:'通知公告',
    message:'系统消息',
  },
  dataAnalyticsReport: {
    title: '数据分析与报告',
    dataOnFilterLife: '数据过滤器寿命',
    selectDevice: '选择设备',
    exportData: '导出数据',
    filterLifePeriod: '过滤器寿命周期',
    
    // 过滤器寿命相关
    filterLife: {
      title: '数据过滤器寿命',
      description: '根据过滤器即将到期的日期选择要导出的设备',
      periods: {
        '1': '16%',
        '2': '33%',
        '3': '50%',
        'oneWeek': '1周',
        'twoWeek': '2周',
        'thirdWeek': '3周',
      }
    },
    
    // 空气质量相关
    airQuality: {
      title: '空气质量数据',
      description: '选择污染等级进行筛选',
      pollutionLevel: '污染等级',
      levels: {
        '1': '优',
        '2': '良',
        '3': '轻度污染',
        '4': '中度污染',
        '5': '重度污染',
        '6': '严重污染'
      },
      selectedCount: '已选择: {count} 个污染等级',
      periods: {
        '1': '1天',
        '3': '3天',
        '7': '7天或更多'
      }
    },
    
    // 活动水平相关
    activityLevel: {
      title: '活动水平',
      description: '根据每周活动水平筛选用户或机器',
      levels: {
        '1': '1小时',
        '2': '1小时 ~ 10小时',
        '3': '10小时 ~ 50小时',
        '4': '50小时 ~ 80小时',
        '5': '80小时或更多'
      }
    },
    
    // 导出参数相关
    exportParams: {
      title: '导出参数',
      description: '选择此导出的参数',
      dataFormat: '数据格式',
      formats: {
        'filter-life': '过滤器寿命',
        'air-quality': '空气质量',
        'activity-level': '活动水平',
        'all': '所有数据'
      }
    },
        
    // 经销商选择相关
    providerSelection: {
      title: '导出范围',
      description: '选择要导出数据的范围',
      allProviders: '全部',
      specificProviders: '指定经销商',
      selectProviders: '选择经销商'
    },
    
    // 经销商选择器
    providerSelector: {
      title: '选择经销商',
      loading: '加载中...',
      selectAll: '全选',
      selectedCount: '({selected}/{total})',
      searchPlaceholder: '搜索经销商名称、国家或地区...',
      noResults: '未找到匹配的经销商'
    },
    
    // 操作按钮
    actions: {
      clearAll: '清除全部',
      exportData: '导出数据',
      exporting: '导出中...',
      cancel: '取消',
      confirm: '确认'
    },
    
    // 设备选择器
    deviceSelector: {
      title: '选择设备型号',
      loading: '加载中...',
      selectAll: '全选',
      selectedCount: '({selected}/{total})'
    },
    
    // 消息提示
    messages: {
      selectDeviceWarning: '请选择要导出的设备',
      selectPollutionLevelWarning: '请选择污染等级',
      exportSuccess: '数据导出成功！',
      exportError: '数据导出失败，请重试',
      downloadError: '文件下载失败'
    }
  },
  
  // 位置概览页面
  locationOverview: {
    title: '位置概览',
    workspace: {
      title: '工作空间',
      placeholder: '请选择工作空间'
    },
    tabs: {
      outdoor: '室外',
      indoor: '室内'
    },
    metrics: {
      aqi: 'AQI',
      pm25: 'PM2.5',
      co2: 'CO₂',
      o3: 'O₃',
      temperature: '温度',
      humidity: '湿度',
      pressure: '气压'
    },
    map: {
      controls: {
        zoomIn: '放大',
        zoomOut: '缩小',
        top: '顶部'
      },
      legend: {
        good: '良好',
        moderate: '中等',
        unhealthySensitive: '对敏感人群不健康',
        unhealthy: '不健康',
        veryUnhealthy: '非常不健康',
        hazardous: '危险'
      }
    },
    weather: {
      title: '天气',
      localTime: '本地时间',
      outdoor: '室外',
      aqi: 'AQI(美国)',
      metrics: {
        temp: '温度',
        humidity: '湿度',
        pm25: 'PM2.5',
        co2: 'CO₂'
      },
      units: {
        celsius: '°C',
        fahrenheit: '°F',
        percent: '%',
        ugm3: 'μg/m³',
        ppm: 'ppm'
      }
    },
    indoor: {
      title: '室内',
      roomSelector: '房间选择',
      deviceInfo: {
        device: '设备',
        totalDevices: '设备总数',
        connection: '连接状态'
      },
      status: {
        allOnline: '全部在线',
        allOffline: '全部离线',
        mixed: '{online} 在线, {offline} 离线',
        noDevices: '无设备'
      },
      airQuality: {
        excellent: '优秀',
        veryGood: '很好',
        good: '好',
        fair: '一般',
        poor: '差',
        veryPoor: '很差',
        noData: '无数据'
      }
    },
    airQuality: {
      good: '良好',
      moderate: '中等',
      unhealthySensitive: '对敏感人群不健康',
      unhealthy: '不健康',
      veryUnhealthy: '非常不健康',
      hazardous: '危险'
    }
  },
  // 帮助分类相关翻译
  helpType: {
    title: '帮助分类',
    typeName: '分类名称',
    parentNode: '父级节点',
    add: '新增分类',
    edit: '编辑分类',
    detail: '分类详情',
    delete: '删除分类',
    addSub: '添加下级',
    confirmDelete: '确定删除吗?',
    deleteSuccess: '删除成功',
    deleteError: '删除失败',
    saveSuccess: '保存成功',
    saveError: '保存失败',
    pleaseEnterTypeName: '请输入分类名称!',
    pleaseSelectParentNode: '请选择父级节点',
    categoryName: '分类名称',
    parentCategory: '父级分类',
    noParent: '无父级',
    level1: '一级分类',
    level2: '二级分类',
    cannotAddSubToLevel2: '二级分类不能添加下级',
  },
  helpInfo: {
    title: '帮助信息',
    helpTitle: '标题',
    helpType: '类别',
    helpContent: '详细内容',
    helpfulNum: '有用计数',
    notHelpfulNum: '无用计数',
    add: '新增帮助',
    edit: '编辑帮助',
    detail: '帮助详情',
    delete: '删除帮助',
    confirmDelete: '是否确认删除',
    deleteSuccess: '删除成功',
    deleteError: '删除失败',
    saveSuccess: '保存成功',
    saveError: '保存失败',
    pleaseEnterTitle: '请输入标题!',
    pleaseSelectType: '请输入类别!',
    pleaseEnterContent: '请输入详细内容!',
  },
  helpFeedback: {
    title: '问题反馈',
    userName: '反馈用户',
    feedbackCategory: '问题分类',
    feedbackContent: '问题描述',
    questionTime: '问题发生时间',
    userPhone: '用户手机',
    userEmail: '用户邮箱',
    questionFrequency: '问题频率',
    isAgree: '同意授权',
    attach: '图片或视频',
    handleStatus: '处理状态',
    handleRecord: '处理记录',
    handleUser: '处理人',
    handleTime: '处理时间',
    add: '新增反馈',
    edit: '编辑反馈',
    detail: '反馈详情',
    delete: '删除反馈',
    audit: '审核',
    auditSuccess: '审核成功',
    auditError: '审核失败',
    pleaseEnterHandleRecord: '请输入处理记录',
    confirmDelete: '是否确认删除',
    deleteSuccess: '删除成功',
    deleteError: '删除失败',
    saveSuccess: '保存成功',
    saveError: '保存失败',
    pleaseEnterUserName: '请输入反馈用户!',
    pleaseEnterFeedbackContent: '请输入问题描述!',
    pleaseEnterQuestionTime: '请选择问题发生时间!',
    pleaseEnterUserPhone: '请输入用户手机!',
    pleaseEnterUserEmail: '请输入用户邮箱!',
    pleaseSelectQuestionFrequency: '请选择问题频率!',
    pleaseSelectIsAgree: '请选择同意授权!',
    pleaseSelectHandleStatus: '请选择处理状态!',
    pleaseEnterHandleUser: '请输入处理人!',
    pleaseEnterHandleTime: '请选择处理时间!',
  },
};
