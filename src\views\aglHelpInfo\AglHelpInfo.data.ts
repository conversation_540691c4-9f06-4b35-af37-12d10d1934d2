import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '标题',
    align:"center",
    sorter: true,
    dataIndex: 'helpTitle'
   },
   {
    title: '类别',
    align:"center",
    sorter: true,
    dataIndex: 'helpType_dictText'
   },
   {
    title: '有用计数',
    align:"center",
    dataIndex: 'helpfulNum'
   },
   {
    title: '无用计数',
    align:"center",
    dataIndex: 'notHelpfulNum'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "标题",
      field: "helpTitle",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
	{
      label: "类别",
      field: 'helpType',
      component: 'JTreeSelect',
      componentProps:{
          dict:"agl_help_type,type_name,id",
          pidValue:"0",
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '标题',
    field: 'helpTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入标题!'},
          ];
     },
  },
  {
    label: '类别',
    field: 'helpType',
    component: 'JTreeSelect',
    componentProps:{
        dict:"agl_help_type,type_name,id",
        pidValue:"0",
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别!'},
          ];
     },
  },
  {
    label: '详细内容',
    field: 'helpContent',
    component: 'JEditor',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入详细内容!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  helpTitle: {title: '标题',order: 0,view: 'text', type: 'string',},
  helpType: {title: '类别',order: 1,view: 'sel_tree', type: 'string',dict: 'agl_help_type,type_name,id', pidValue: '0',},
  helpfulNum: {title: '有用计数',order: 3,view: 'number', type: 'number',},
  notHelpfulNum: {title: '无用计数',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}