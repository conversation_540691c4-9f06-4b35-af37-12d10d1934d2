import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

//列表数据
export const columns: BasicColumn[] = [
   {
    title: t('airgle.helpInfo.helpTitle'),
    align:"center",
    sorter: true,
    dataIndex: 'helpTitle'
   },
   {
    title: t('airgle.helpInfo.helpType'),
    align:"center",
    sorter: true,
    dataIndex: 'helpType_dictText'
   },
   {
    title: t('airgle.helpInfo.helpfulNum'),
    align:"center",
    dataIndex: 'helpfulNum'
   },
   {
    title: t('airgle.helpInfo.notHelpfulNum'),
    align:"center",
    dataIndex: 'notHelpfulNum'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: t('airgle.helpInfo.helpTitle'),
      field: "helpTitle",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
	{
      label: t('airgle.helpInfo.helpType'),
      field: 'helpType',
      component: 'JTreeSelect',
      componentProps:{
          dict:"agl_help_type,type_name,id",
          pidValue:"0",
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: t('airgle.helpInfo.helpTitle'),
    field: 'helpTitle',
    component: 'Input',
    dynamicRules: ({model: _model, schema: _schema}) => {
          return [
                 { required: true, message: t('airgle.helpInfo.pleaseEnterTitle')},
          ];
     },
  },
  {
    label: t('airgle.helpInfo.helpType'),
    field: 'helpType',
    component: 'JTreeSelect',
    componentProps:{
        dict:"agl_help_type,type_name,id",
        pidValue:"0",
    },
    dynamicRules: ({model: _model, schema: _schema}) => {
          return [
                 { required: true, message: t('airgle.helpInfo.pleaseSelectType')},
          ];
     },
  },
  {
    label: t('airgle.helpInfo.helpContent'),
    field: 'helpContent',
    component: 'JEditor',
    dynamicRules: ({model: _model, schema: _schema}) => {
          return [
                 { required: true, message: t('airgle.helpInfo.pleaseEnterContent')},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  helpTitle: {title: t('airgle.helpInfo.helpTitle'),order: 0,view: 'text', type: 'string',},
  helpType: {title: t('airgle.helpInfo.helpType'),order: 1,view: 'sel_tree', type: 'string',dict: 'agl_help_type,type_name,id', pidValue: '0',},
  helpfulNum: {title: t('airgle.helpInfo.helpfulNum'),order: 3,view: 'number', type: 'number',},
  notHelpfulNum: {title: t('airgle.helpInfo.notHelpfulNum'),order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}