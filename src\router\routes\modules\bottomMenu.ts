import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const bottomMenu: AppRouteModule = {
  path: '/bottom-menu',
  name: 'BottomMenu',
  component: LAYOUT,
  meta: {
    hideMenu: true,
    title: 'Bottom Menu',
  },
  children: [
    {
      path: 'help',
      name: 'HelpPage',
      component: () => import('/@/views/helpCenter/index.vue'),
      meta: {
        title: 'Need help?',
        hideMenu: true,
      },
    },
  ],
};

export default bottomMenu; 