/**
 * IP地址和地理位置相关工具函数
 */

// 缓存机制，避免重复请求
const ipLocationCache = new Map<string, any>();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存

/**
 * 检查缓存是否有效
 */
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

// 海外国家/地区代码列表（主要海外地区）
const OVERSEAS_COUNTRIES = [
  'US', // 美国
  'CA', // 加拿大
  'GB', // 英国
  'FR', // 法国
  'DE', // 德国
  'IT', // 意大利
  'ES', // 西班牙
  'NL', // 荷兰
  'BE', // 比利时
  'CH', // 瑞士
  'AT', // 奥地利
  'SE', // 瑞典
  'NO', // 挪威
  'DK', // 丹麦
  'FI', // 芬兰
  'IE', // 爱尔兰
  'PT', // 葡萄牙
  'GR', // 希腊
  'PL', // 波兰
  'CZ', // 捷克
  'HU', // 匈牙利
  'RO', // 罗马尼亚
  'BG', // 保加利亚
  'HR', // 克罗地亚
  'SI', // 斯洛文尼亚
  'SK', // 斯洛伐克
  'LT', // 立陶宛
  'LV', // 拉脱维亚
  'EE', // 爱沙尼亚
  'CY', // 塞浦路斯
  'MT', // 马耳他
  'LU', // 卢森堡
  'AU', // 澳大利亚
  'NZ', // 新西兰
  'JP', // 日本
  'KR', // 韩国
  'SG', // 新加坡
  'MY', // 马来西亚
  'TH', // 泰国
  'VN', // 越南
  'PH', // 菲律宾
  'ID', // 印度尼西亚
  'IN', // 印度
  'BR', // 巴西
  'AR', // 阿根廷
  'CL', // 智利
  'CO', // 哥伦比亚
  'PE', // 秘鲁
  'MX', // 墨西哥
  'ZA', // 南非
  'EG', // 埃及
  'NG', // 尼日利亚
  'KE', // 肯尼亚
  'GH', // 加纳
  'UG', // 乌干达
  'TZ', // 坦桑尼亚
  'ET', // 埃塞俄比亚
  'DZ', // 阿尔及利亚
  'MA', // 摩洛哥
  'TN', // 突尼斯
  'LY', // 利比亚
  'SD', // 苏丹
  'SS', // 南苏丹
  'CF', // 中非共和国
  'TD', // 乍得
  'NE', // 尼日尔
  'ML', // 马里
  'BF', // 布基纳法索
  'CI', // 科特迪瓦
  'SN', // 塞内加尔
  'GN', // 几内亚
  'SL', // 塞拉利昂
  'LR', // 利比里亚
  'TG', // 多哥
  'BJ', // 贝宁
  'CM', // 喀麦隆
  'GQ', // 赤道几内亚
  'GA', // 加蓬
  'CG', // 刚果共和国
  'CD', // 刚果民主共和国
  'AO', // 安哥拉
  'ZM', // 赞比亚
  'ZW', // 津巴布韦
  'BW', // 博茨瓦纳
  'NA', // 纳米比亚
  'SZ', // 斯威士兰
  'LS', // 莱索托
  'MG', // 马达加斯加
  'MU', // 毛里求斯
  'SC', // 塞舌尔
  'KM', // 科摩罗
  'DJ', // 吉布提
  'SO', // 索马里
  'ER', // 厄立特里亚
  'RW', // 卢旺达
  'BI', // 布隆迪
  'MW', // 马拉维
  'MZ', // 莫桑比克
  'ZW', // 津巴布韦
  'ZM', // 赞比亚
  'AO', // 安哥拉
  'CD', // 刚果民主共和国
  'CG', // 刚果共和国
  'GA', // 加蓬
  'GQ', // 赤道几内亚
  'CM', // 喀麦隆
  'BJ', // 贝宁
  'TG', // 多哥
  'LR', // 利比里亚
  'SL', // 塞拉利昂
  'GN', // 几内亚
  'SN', // 塞内加尔
  'CI', // 科特迪瓦
  'BF', // 布基纳法索
  'ML', // 马里
  'NE', // 尼日尔
  'TD', // 乍得
  'CF', // 中非共和国
  'SS', // 南苏丹
  'SD', // 苏丹
  'LY', // 利比亚
  'TN', // 突尼斯
  'MA', // 摩洛哥
  'DZ', // 阿尔及利亚
  'ET', // 埃塞俄比亚
  'TZ', // 坦桑尼亚
  'UG', // 乌干达
  'GH', // 加纳
  'KE', // 肯尼亚
  'NG', // 尼日利亚
  'EG', // 埃及
  'ZA', // 南非
  'PE', // 秘鲁
  'CO', // 哥伦比亚
  'CL', // 智利
  'AR', // 阿根廷
  'BR', // 巴西
  'IN', // 印度
  'ID', // 印度尼西亚
  'PH', // 菲律宾
  'VN', // 越南
  'TH', // 泰国
  'MY', // 马来西亚
  'SG', // 新加坡
  'KR', // 韩国
  'JP', // 日本
  'NZ', // 新西兰
  'AU', // 澳大利亚
  'LU', // 卢森堡
  'MT', // 马耳他
  'CY', // 塞浦路斯
  'EE', // 爱沙尼亚
  'LV', // 拉脱维亚
  'LT', // 立陶宛
  'SK', // 斯洛伐克
  'SI', // 斯洛文尼亚
  'HR', // 克罗地亚
  'BG', // 保加利亚
  'RO', // 罗马尼亚
  'HU', // 匈牙利
  'CZ', // 捷克
  'PL', // 波兰
  'GR', // 希腊
  'PT', // 葡萄牙
  'IE', // 爱尔兰
  'FI', // 芬兰
  'DK', // 丹麦
  'NO', // 挪威
  'SE', // 瑞典
  'AT', // 奥地利
  'CH', // 瑞士
  'BE', // 比利时
  'NL', // 荷兰
  'ES', // 西班牙
  'IT', // 意大利
  'DE', // 德国
  'FR', // 法国
  'GB', // 英国
  'CA', // 加拿大
  'US', // 美国
];

/**
 * 获取用户IP地址
 * @returns Promise<string> IP地址
 */
export const getUserIP = async (): Promise<string> => {
  try {
    // 使用简单的IP查询服务
    const response = await fetch('https://api.ipify.org?format=json');
    if (response.ok) {
      const data = await response.json();
      return data.ip;
    }
    return '';
  } catch (error) {
    console.error('获取IP地址失败:', error);
    return '';
  }
};

/**
 * 获取IP地址的地理位置信息
 * @param ip IP地址
 * @returns Promise<any> 地理位置信息
 */
export const getIPLocation = async (ip: string): Promise<any> => {
  try {
    // 检查缓存
    const cached = ipLocationCache.get(ip);
    if (cached && isCacheValid(cached.timestamp)) {
      console.log('使用缓存的IP地理位置信息:', ip);
      return cached.data;
    }

    // 使用可靠的IP地理位置查询服务
    const services = [
      `https://ipapi.co/${ip}/json/`,
      `https://ip-api.com/json/${ip}`
    ];

    for (const serviceUrl of services) {
      try {
        const response = await fetch(serviceUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
          const data = await response.json();
          let locationData: any = null;
          
          if (serviceUrl.includes('ipapi.co')) {
            locationData = {
              country_code: data.country_code,
              country_name: data.country_name,
              region: data.region,
              city: data.city
            };
          } else if (serviceUrl.includes('ip-api.com')) {
            locationData = {
              country_code: data.countryCode,
              country_name: data.country,
              region: data.regionName,
              city: data.city
            };
          }
          
          if (locationData) {
            ipLocationCache.set(ip, {
              data: locationData,
              timestamp: Date.now()
            });
          }
          
          return locationData;
        }
      } catch (serviceError) {
        console.warn(`IP地理位置服务 ${serviceUrl} 请求失败:`, serviceError);
        continue;
      }
    }
    
    console.error('所有IP地理位置查询服务都失败');
    return null;
  } catch (error) {
    console.error('获取IP地理位置失败:', error);
    return null;
  }
};

/**
 * 判断是否为海外IP
 * @param countryCode 国家代码
 * @returns boolean 是否为海外IP
 */
export const isOverseasIP = (countryCode: string): boolean => {
  if (!countryCode) return false;
  
  // 检查是否在海外国家列表中
  return OVERSEAS_COUNTRIES.includes(countryCode.toUpperCase());
};

/**
 * 获取用户IP并判断是否为海外IP
 * @returns Promise<{ip: string, isOverseas: boolean, countryCode: string}>
 */
export const getUserIPAndLocation = async (): Promise<{
  ip: string;
  isOverseas: boolean;
  countryCode: string;
  countryName?: string;
}> => {
  try {
    const ip = await getUserIP();
    if (!ip) {
      return {
        ip: '',
        isOverseas: true, // 默认隐藏QQ和微信登录选项
        countryCode: '',
        countryName: ''
      };
    }

    const location = await getIPLocation(ip);
    if (!location) {
      return {
        ip,
        isOverseas: true, // 默认隐藏QQ和微信登录选项
        countryCode: '',
        countryName: ''
      };
    }

    const countryCode = location.country_code || '';
    const isOverseas = isOverseasIP(countryCode);

    return {
      ip,
      isOverseas,
      countryCode,
      countryName: location.country_name || ''
    };
  } catch (error) {
    console.error('获取用户IP和地理位置失败:', error);
    return {
      ip: '',
      isOverseas: true, // 默认隐藏QQ和微信登录选项
      countryCode: '',
      countryName: ''
    };
  }
}; 