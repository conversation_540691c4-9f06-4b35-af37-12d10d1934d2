import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const locationOverview: AppRouteModule = {
  path: '/location-overview',
  name: 'LocationOverview',
  component: LAYOUT,
  redirect: '/location-overview/index',
  meta: {
    orderNo: 20,
    icon: 'ion:location-outline',
    title: t('routes.location.overview'),
  },
  children: [
    {
      path: 'index',
      name: 'LocationOverviewPage',
      component: () => import('/@/views/locationOverview/index.vue'),
      meta: {
        title: t('routes.location.overview'),
      },
    },
  ],
};

export default locationOverview; 