<template>
  <div :class="[prefixCls, themeClass]">
    <div class="bottom-menu-group help-section">
      <div class="bottom-menu-item" @click="handleHelpClick">
        <img src="/@/assets/images/help.png" class="menu-icon" alt="help" />
        <div class="menu-text">
          <span class="bottom-menu-title" style="font-size: 14px !important">Need help?</span>
          <span class="bottom-menu-subtitle" style="font-size: 14px">Check the knowledge base</span>
        </div>
      </div>
    </div>

    <div class="bottom-menu-group action-section">
      <div class="bottom-menu-item logout" @click="handleLogout">
        <img src="/@/assets/images/logout.png" class="menu-icon" alt="logout" style="width: 20px; height: 20px;" />
        <span class="bottom-menu-title">Log Out</span>
      </div>
      <div class="bottom-menu-item contact" @click="handleContactClick">
        <img src="/src/assets/images/email.png" alt="Message" style="width: 16px; height: 13px;" />
        <span class="bottom-menu-title">&nbsp;Contact Us</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { useDesign } from '/@/hooks/web/useDesign';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';

import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { unref } from 'vue';

export default defineComponent({
  name: 'BottomMenuItems',
  components: {
  },
  setup() {
    const { prefixCls } = useDesign('bottom-menu');
    const router = useRouter();
    const userStore = useUserStore();
    const { getMenuTheme } = useMenuSetting();

    const themeClass = computed(() => {
      return unref(getMenuTheme) === 'light' ? 'bright' : '';
    });

    const handleHelpClick = () => {
      router.push('/helpCenter/index');
    };

    const handleLogout = async () => {
      await userStore.logout(true);
    };

    const handleContactClick = () => {
      // Placeholder for future implementation
      console.log('Contact Us clicked');
    };

    return {
      prefixCls,
      themeClass,
      handleHelpClick,
      handleLogout,
      handleContactClick,
    };
  },
});
</script>

<style lang="less" scoped>
@import '/@/design/ant/index.less';

@prefix-cls: ~'@{namespace}-bottom-menu';

.@{prefix-cls} {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 25em;
  z-index: 1;
  padding: 8px 16px;
  transition: all 0.2s;

  &.bright {
    background-color: @white;

    .bottom-menu-item {
      color: @text-color;



      &.contact {
        background-color: #00a0e9;
        color: @white;

       
      }
    }
  }

  .bottom-menu-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
  }

  .bottom-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    color: @text-color-dark;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 4px;

    &.contact {
      background-color: @primary-color;
      color: @white;
    }

    .anticon {
      font-size: 16px;
      margin-right: 8px;
    }

    .menu-icon {
      width: 30px;
      height: 30px;
      margin-right: 10px;
    }

    .menu-text {
      display: flex;
      flex-direction: column;
    }

    .bottom-menu-title {
      font-size: 14px;
      line-height: 1.2;
    }

    .bottom-menu-subtitle {
      font-size: 14px;
      color: @primary-color;
    }
  }

  .help-section {
    padding: 10px;
    background-color: #f8f8f8;
    justify-content: center;

    .bottom-menu-item {
      padding: 8px 0;
    }
  }

  .action-section {
    padding: 20px 0;

    .bottom-menu-item {
      display: inline-flex;
      align-items: center;
    }
  }
}
</style>