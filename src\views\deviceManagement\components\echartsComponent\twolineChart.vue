<template>
  <div class="chart-wrapper flex-align">
    <div class="chart-wrapper-left">
      <div class="wrapper-up flex">
        <img :src="iconSrc" class="wrapper-up-img" :alt="title" />
        <div class="baseline">
          <div class="text-1">{{ value }}</div>
          <div class="text-2">{{ unit }}</div>
        </div>
      </div>
      <div class="wrapper-down">
        <div class="down-1 display-center">{{ title }}</div>
        <div class="down-2 display-center">{{ status }}</div>
      </div>
    </div>
    <div ref="chartRef" style="width: 100%; height: 270px; flex: 1"></div>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  // Basic info
  title: {
    type: String,
    default: 'PM2.5'
  },
  value: {
    type: [Number, String],
    default: 28
  },
  unit: {
    type: String,
    default: '%'
  },
  status: {
    type: String,
    default: 'Good'
  },
  iconSrc: {
    type: String,
    default: ''
  },
  // Chart data
  outdoorData: {
    type: Array,
    default: () => [12, 12, 12, 23, 34, 45, 23, 23, 23, 23, 23, 23, 23, 12, 12, 23, 23, 34, 23, 12, 23, 12, 21, 18]
  },
  indoorData: {
    type: Array,
    default: () => [23, 12, 12, 23, 23, 34, 23, 12, 23, 12, 21, 18, 12, 12, 12, 23, 34, 45, 23, 23, 23, 23, 23, 23]
  },
  yAxisUnit: {
    type: String,
    default: '°F'
  },
  outdoorMaxValue: {
    type: String,
    default: '23°F'
  },
  indoorMaxValue: {
    type: String,
    default: '25°F'
  }
});

const chartRef = ref(null);
let myChart = null;

const renderChart = () => {
  if (!chartRef.value) return;
  if (!myChart) {
    myChart = echarts.init(chartRef.value);
  }
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: [
        '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11',
        '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24(h)'
      ],
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: `{value}${props.yAxisUnit}`,
      },
    },
    series: [
      {
        name: 'Outdoor',
        type: 'line',
        data: props.outdoorData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#4b9cff',
        },
        markPoint: {
          data: [
            {
              type: 'max',
              name: 'Max Temperature',
              label: {
                formatter: props.outdoorMaxValue,
                position: 'top',
              },
            },
          ],
        },
      },
      {
        name: 'Indoor',
        type: 'line',
        data: props.indoorData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#58c2a2',
        },
        markPoint: {
          data: [
            {
              type: 'max',
              name: 'Max Temperature',
              label: {
                formatter: props.indoorMaxValue,
                position: 'top',
              },
            },
          ],
        },
      },
    ],
  };
  myChart.setOption(option);
};

let resizeObserver = null;

// Watch for data changes to update the chart
watch([
  () => props.outdoorData,
  () => props.indoorData,
  () => props.yAxisUnit,
  () => props.outdoorMaxValue,
  () => props.indoorMaxValue
], () => {
  renderChart();
}, { deep: true });

onMounted(() => {
  renderChart();
  resizeObserver = new ResizeObserver(() => {
    myChart?.resize();
  });
  resizeObserver.observe(chartRef.value);
});

onUnmounted(() => {
  resizeObserver?.disconnect();
  myChart?.dispose();
});
</script>

<style lang="less" scoped>
  .chart-wrapper {
    .chart-wrapper-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      .wrapper-up {
       margin-bottom: 10px;
        .baseline {
          margin-left: 10px;
        }
        .text-1 {
          font-size: 28px;
          color: #000;
        }
        .text-2 {
          font-size: 12px;
        }
      }
      .wrapper-down {
        display: flex;
        .down-1 {          
          border-radius: 10px 0 0 10px;
          border: 1px solid #31bb77;
          box-sizing: border-box;
          width: 66px;
          height: 30px;
          color: #000;
        }
        .down-2 {
          border-radius: 0 10px 10px 0;          
          background: #31bb77;
          box-sizing: border-box;
          width: 66px;
          height: 30px;
          color: #fff;
        }
      }
    }
  }
</style>
