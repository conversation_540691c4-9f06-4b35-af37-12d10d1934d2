/**
 * 群组管理相关API
 */
import { defHttp } from '/@/utils/http/axios';

// API接口地址
const API = {
  GROUP_LIST: '/airgle/aglGroup/list', // 群组分页列表查询
  GROUP_ADD: '/airgle/aglGroup/add', // 创建群组
  GROUP_DELETE_BATCH: '/airgle/aglGroup/deleteBatch', // 批量删除群组
  GROUP_REMOVE_DEVICES: '/airgle/aglGroup/removeDevicesFromGroup', // 从群组中移除设备
};

/**
 * 群组列表查询参数接口
 */
export interface GroupListParams {
  id?: string;              // 主键
  createBy?: string;        // 创建人
  createTime?: string;      // 创建日期
  updateBy?: string;        // 更新人
  updateTime?: string;      // 更新日期
  sysOrgCode?: string;      // 所属部门
  groupName?: string;       // 群组名称
  workspaceName?: string;   // 所属空间
  remark?: string;          // 群组描述
  iconUrl?: string;         // 图片信息
  groupType?: number;       // 群组协议类型 0: Wi-Fi, 1: 蓝牙低功耗, 2: Zigbee, 3: 蓝牙 mesh
  productId?: string;       // 产品ID
  type?: number;            // 群组分组类型 0: 直连云设备, 1: 非直连云设备，需要通过网关设备连云的设备
  localKey?: string;        // 对应的 localKey
  pv?: string;              // MQTT 协议版本
  pageNo?: number;          // 页码
  pageSize?: number;        // 每页大小
  deviceCount?: number;
}

/**
 * 群组数据接口
 */
export interface GroupRecord {
  id: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  sysOrgCode: string;
  groupName: string;
  workspaceName: string;
  workspaceName_dictText?: string;
  remark: string;
  iconUrl: string;
  groupType: number;
  productId: string;
  type: number;
  localKey: string;
  pv: string;
  deviceCount?: number;        // 设备数量
  groupStatus?: GroupStatusItem[]; // 群组状态列表
}

/**
 * 分页排序信息
 */
export interface OrderInfo {
  column: string;
  asc: boolean;
}

/**
 * 群组列表返回结果
 */
export interface GroupListResult {
  records: GroupRecord[];
  total: number;
  size: number;
  current: number;
  orders: OrderInfo[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  optimizeJoinOfCountSql: boolean;
  maxLimit: number;
  countId: string;
  pages: number;
}

/**
 * API响应格式
 */
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

/**
 * 获取群组列表
 * @param params 查询参数
 * @returns 群组列表数据
 */
export const getGroupList = (params: GroupListParams): Promise<GroupListResult> => {
  return defHttp.get<GroupListResult>({
    url: API.GROUP_LIST,
    params,
  });
};

/**
 * 群组协议类型枚举
 */
export const GroupTypeEnum = {
  WIFI: 0,        // Wi-Fi
  BLE: 1,         // 蓝牙低功耗
  ZIGBEE: 2,      // Zigbee
  MESH: 3,        // 蓝牙 mesh
} as const;

/**
 * 群组分组类型枚举
 */
export const GroupCategoryEnum = {
  DIRECT: 0,      // 直连云设备
  GATEWAY: 1,     // 非直连云设备，需要通过网关设备连云的设备
} as const;

/**
 * 获取群组协议类型显示名称
 */
export const getGroupTypeName = (type: number): string => {
  const typeMap = {
    [GroupTypeEnum.WIFI]: 'Wi-Fi',
    [GroupTypeEnum.BLE]: '蓝牙低功耗',
    [GroupTypeEnum.ZIGBEE]: 'Zigbee',
    [GroupTypeEnum.MESH]: '蓝牙 mesh',
  };
  return typeMap[type] || '未知';
};

/**
 * 获取群组分组类型显示名称
 */
export const getGroupCategoryName = (type: number): string => {
  const typeMap = {
    [GroupCategoryEnum.DIRECT]: '直连云设备',
    [GroupCategoryEnum.GATEWAY]: '非直连云设备',
  };
  return typeMap[type] || '未知';
};


// 查询群组下设备列表
export const getDevicesByGroupId = (groupId: string) =>
  defHttp.get({
    url: '/airgle/aglGroup/queryAglGroupDevicesByMainId',
    params: { id: groupId }
  });

/**
 * 群组状态项接口
 */
export interface GroupStatusItem {
  code: string;    // 状态代码
  name: string;    // 状态名称
  type: string;    // 状态类型 (bool/enum)
  value: string;   // 状态值
}

/**
 * 群组设备控制参数接口
 */
export interface GroupCommandParams {
  speed?: string;    // 风速设置
  switch?: boolean;  // 开关状态
  uv?: boolean;      // UV状态
  lock?: boolean;    // 童锁状态
}

/**
 * 群组设备控制响应接口
 */
export interface GroupCommandResult {
  success: boolean;
  message: string;
  code: number;
  result: boolean;  // 修改为boolean类型，因为实际返回的是true/false
  timestamp: number;
}

/**
 * 群组设备同步控制
 * @param groupId 群组ID
 * @param params 控制参数
 * @returns 控制结果
 */
export const sendGroupCommand = (groupId: string, params: GroupCommandParams): Promise<boolean> => {
  return defHttp.post<boolean>({
    url: '/airgle/aglGroup/command',
    params: { groupId },
    data: params,
  });
};

/**
 * 群组设备项接口
 */
export interface GroupDeviceItem {
  deviceId: string;      // 设备id
  status: string;        // 设备在群组中的状态
  displayOrder: number;  // 设备排序
}

/**
 * 创建群组参数接口
 */
export interface CreateGroupParams {
  workspaceName: string;           // 所属空间ID
  productId?: string;              // 产品 ID
  aglGroupDevicesList?: GroupDeviceItem[]; // 群组设备列表
  groupName: string;               // 群组名称
}

/**
 * 创建群组
 * @param params 创建群组参数
 * @returns 创建结果
 */
export const createGroup = (params: CreateGroupParams): Promise<any> => {
  return defHttp.post({
    url: API.GROUP_ADD,
    data: params,
  });
};

/**
 * 批量删除群组
 * @param ids 群组ID数组，用逗号分隔
 * @returns 删除结果
 */
export const deleteGroupsBatch = (ids: string): Promise<any> => {
  return defHttp.delete({
    url: `${API.GROUP_DELETE_BATCH}?ids=${ids}`,
  });
};

/**
 * 从群组中移除设备
 * @param groupId 群组ID
 * @param deviceIds 设备ID数组
 * @returns 移除结果
 */
export const removeDevicesFromGroup = (groupId: string, deviceIds: string[]): Promise<any> => {
  return defHttp.delete({
    url: `${API.GROUP_REMOVE_DEVICES}/${groupId}`,
    data: { device_ids: deviceIds },
  });
};