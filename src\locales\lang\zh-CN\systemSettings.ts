export default {
  persona: '个人信息',
  account: '账号与安全',
  device: '设备更新',
  touchTone: '面板触控音',
  notification: '系统报警',
  temperature: '温度单位',
  debug: '多语言调试模式',
  features: '更多功能',
  about: '关于',
  privacy: '隐私设置',
  policy: '隐私政策管理',
  network: '网络诊断',
  cache: '清除缓存',
  thirdPartyApp: '第三方APP',
  accountSecurity: '账号安全',
  personal: '个人信息',
  // 第三方绑定相关
  accountBinding: '账号绑定',
  qqBinding: 'QQ绑定',
  appleBinding: '苹果绑定',
  wechatBinding: '微信绑定',
  dingtalkBinding: '钉钉绑定',
  googleBinding: 'Google绑定',
  enterpriseWechatBinding: '企业微信绑定',
  // 绑定状态
  bound: '已绑定',
  unbound: '未绑定',
  bind: '绑定',
  unbind: '解绑',
  // 绑定提示
  bindSuccess: '绑定成功',
  unbindSuccess: '解绑成功',
  bindFailed: '绑定失败',
  unbindFailed: '解绑失败',
  confirmUnbind: '确定要解绑吗',
  // 第三方平台名称
  qq: 'QQ',
  apple: '苹果',
  wechat: '微信',
  dingtalk: '钉钉',
  google: 'Google',
  enterpriseWechat: '企业微信',
  // 账户设置相关
  accountTitle: '账户',
  phone: '手机',
  email: '邮箱',
  password: '密码',
  modify: '修改',
  notFilled: '未填写',
  accountCancellation: '账户注销',
  cancel: '注销',
  verify: '验证',
  // 基础设置相关
  detailedInfo: '详细资料',
  contactInfo: '联系信息',
  birthday: '生日',
  gender: '性别',
  position: '职位',
  edit: '编辑',
  editName: '编辑姓名',
  pleaseEnterName: '请输入姓名',
  usageTime: '使用',
  days: '天',
  male: '男',
  female: '女',
}; 