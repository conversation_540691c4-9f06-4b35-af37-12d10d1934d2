/**
 * 用户访问控制相关API
 */
import { defHttp } from '/@/utils/http/axios';

// API 接口地址
enum Api {
  // 用户信息相关
  GET_USER_INFO = '/user/info',
  UPDATE_USER_INFO = '/user/update',

  // 设备位置相关
  GET_DEVICE_LOCATIONS = '/device-location/list',
  CREATE_DEVICE_LOCATION = '/device-location/create',
  UPDATE_DEVICE_LOCATION = '/device-location/update',
  DELETE_DEVICE_LOCATION = '/device-location/delete',

  // 位置成员相关
  GET_LOCATION_MEMBERS = '/device-location/members',
  ADD_LOCATION_MEMBER = '/device-location/add-member',
  REMOVE_LOCATION_MEMBER = '/device-location/remove-member',

  // 群组相关
  JOIN_GROUP = '/group/join',
  LEAVE_GROUP = '/group/leave',
  GET_GROUP_INFO = '/group/info',

  // 消息中心相关
  GET_MESSAGES = '/sys/sysAnnouncementSend/getMyAnnouncementSend',
  MARK_MESSAGE_READ = '/message/read',
  GET_MESSAGE_COUNT = '/message/count',
  UPDATE_MESSAGE_STATUS = '/sys/sysAnnouncementSend/editByAnntIdAndUserId',
  SYNC_MESSAGE = '/sys/annountCement/syncNotic',
  READ_ALL_MESSAGES = '/sys/sysAnnouncementSend/readAll',

  // 工作空间相关
  GET_WORKSPACE_LIST = '/airgle/aglWorkspace/list'
}

// 用户信息接口
export interface UserInfo {
  id: string;
  nickname: string;
  email: string;
  role: string;
  timezone: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

// 设备位置接口
export interface DeviceLocation {
  id: string;
  name: string;
  groupName: string;
  address: string;
  location: string;
  roomCount: number;
  memberCount: number;
  createdAt: string;
  updatedAt: string;
}

// 位置成员接口
export interface LocationMember {
  id: string;
  userId: string;
  locationId: string;
  name: string;
  email: string;
  role: 'Admin' | 'Member' | 'Viewer';
  avatar?: string;
  joinedAt: string;
}

// 群组信息接口
export interface GroupInfo {
  id: string;
  name: string;
  description?: string;
  memberCount: number;
  createdAt: string;
}

// 消息接口
export interface Message {

  "id": string,
  "anntId": string,
  "userId": string,
  "titile": string,
  "msgContent": string,
  "sender": string,
  "priority": string,
  "readFlag": number,
  "sendTime": string,
  "pageNo": number,
  "pageSize": number,
  "msgCategory": string,
  "busId": string,
  "busType": string,
  "openType": string,
  "openPage": string,
  "bizSource": string,
  "msgAbstract": string,
  "sendTimeBegin": string,
  "sendTimeEnd": string

}

// 工作空间接口（对应接口返回数据）
export interface WorkspaceRecord {
  id: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  sysOrgCode: string;
  workspaceName: string;
  workspaceLocation: string;
  longitude: number;
  latitude: number;
  remark: string;
}

// 工作空间列表响应接口
export interface WorkspaceListResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    records: WorkspaceRecord[];
    total: number;
    size: number;
    current: number;
    orders: Array<{
      column: string;
      asc: boolean;
    }>;
    optimizeCountSql: boolean;
    searchCount: boolean;
    optimizeJoinOfCountSql: boolean;
    maxLimit: number;
    countId: string;
    pages: number;
  };
  timestamp: number;
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return defHttp.get<UserInfo>({
    url: Api.GET_USER_INFO
  });
};

/**
 * 更新用户信息
 */
export const updateUserInfo = (data: Partial<UserInfo>) => {
  return defHttp.put<UserInfo>({
    url: Api.UPDATE_USER_INFO,
    data
  });
};

/**
 * 获取设备位置列表
 */
export const getDeviceLocations = () => {
  return defHttp.get<DeviceLocation[]>({
    url: Api.GET_DEVICE_LOCATIONS
  });
};

/**
 * 创建设备位置
 */
export const createDeviceLocation = (data: {
  groupName: string;
  address: string;
  locations: string[];
}) => {
  return defHttp.post<DeviceLocation>({
    url: Api.CREATE_DEVICE_LOCATION,
    data
  });
};

/**
 * 更新设备位置
 */
export const updateDeviceLocation = (id: string, data: Partial<DeviceLocation>) => {
  return defHttp.put<DeviceLocation>({
    url: `${Api.UPDATE_DEVICE_LOCATION}/${id}`,
    data
  });
};

/**
 * 删除设备位置
 */
export const deleteDeviceLocation = (id: string) => {
  return defHttp.delete({
    url: `${Api.DELETE_DEVICE_LOCATION}/${id}`
  });
};

/**
 * 获取位置成员列表
 */
export const getLocationMembers = (locationId: string) => {
  return defHttp.get<LocationMember[]>({
    url: `${Api.GET_LOCATION_MEMBERS}/${locationId}`
  });
};

/**
 * 添加位置成员
 */
export const addLocationMember = (data: {
  locationId: string;
  email: string;
  role: string;
}) => {
  return defHttp.post<LocationMember>({
    url: Api.ADD_LOCATION_MEMBER,
    data
  });
};

/**
 * 移除位置成员
 */
export const removeLocationMember = (locationId: string, memberId: string) => {
  return defHttp.delete({
    url: `${Api.REMOVE_LOCATION_MEMBER}/${locationId}/${memberId}`
  });
};

/**
 * 加入群组
 */
export const joinGroup = (invitationCode: string) => {
  return defHttp.post<GroupInfo>({
    url: Api.JOIN_GROUP,
    data: { invitationCode }
  });
};

/**
 * 离开群组
 */
export const leaveGroup = (groupId: string) => {
  return defHttp.delete({
    url: `${Api.LEAVE_GROUP}/${groupId}`
  });
};

/**
 * 获取群组信息
 */
export const getGroupInfo = (groupId: string) => {
  return defHttp.get<GroupInfo>({
    url: `${Api.GET_GROUP_INFO}/${groupId}`
  });
};

/**
 * 获取消息列表
 */
export const getMessages = (params?: {
  page?: number;
  pageSize?: number;
  type?: string;
  isRead?: boolean;
  msgCategory?: number;
}) => {
  return defHttp.get({
    url: Api.GET_MESSAGES,
    params
  });
};

/**
 * 标记消息为已读
 */
export const markMessageRead = (messageId: string) => {
  return defHttp.put({
    url: `${Api.MARK_MESSAGE_READ}/${messageId}`
  });
};


/**
 * 获取未读消息数量
 */
export const getUnreadMessageCount = () => {
  return defHttp.get<{ count: number }>({
    url: Api.GET_MESSAGE_COUNT
  });
};

/**
 * 更新消息状态
 */
export const updateMessageStatus = (anntId: string) => {
  return defHttp.put({
    url: Api.UPDATE_MESSAGE_STATUS,
    params: { anntId }
  });
};

/**
 * 同步消息
 */
export const syncMessage = (anntId: string) => {
  return defHttp.get({
    url: Api.SYNC_MESSAGE,
    params: { anntId }
  });
};

/**
 * 获取工作空间列表
 */
export const getWorkspaceList = (params?: {
  pageNo?: number;
  pageSize?: number;
}) => {
  return defHttp.get<WorkspaceListResponse['result']>({
    url: Api.GET_WORKSPACE_LIST,
    params
  });
};

// 临时别名以解决缓存问题
export const getMessageDetail = syncMessage;

/**
 * 标记所有消息为已读
 */
export const readAllMessages = () => {
  return defHttp.put({
    url: Api.READ_ALL_MESSAGES
  });
};
