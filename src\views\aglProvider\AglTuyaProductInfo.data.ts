import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'SN系列号',
    align:"center",
    dataIndex: 'sn'
   },
   {
    title: 'MAC地址',
    align:"center",
    dataIndex: 'mac'
   },
   {
    title: 'UUID',
    align:"center",
    dataIndex: 'uuid'
   },
   {
    title: '烧录时间',
    align:"center",
    dataIndex: 'burnTime'
   },
   {
    title: '是否绑定经销商',
    align:"center",
    dataIndex: 'isBind_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "SN系列号",
      field: "sn",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
     {
      label: "MAC地址",
      field: "mac",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
     {
      label: "烧录时间",
      field: "burnTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'SN系列号',
    field: 'sn',
    component: 'Input',
  },
  {
    label: 'MAC地址',
    field: 'mac',
    component: 'Input',
  },
  {
    label: 'UUID',
    field: 'uuid',
    component: 'Input',
  },
  {
    label: '烧录时间',
    field: 'burnTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '是否绑定经销商',
    field: 'isBind',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"tuya_productI_is_bind"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否绑定经销商!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sn: {title: 'SN系列号',order: 0,view: 'text', type: 'string',},
  mac: {title: 'MAC地址',order: 1,view: 'text', type: 'string',},
  uuid: {title: 'UUID',order: 2,view: 'text', type: 'string',},
  burnTime: {title: '烧录时间',order: 3,view: 'datetime', type: 'string',},
  isBind: {title: '是否绑定经销商',order: 4,view: 'list', type: 'string',dictCode: 'tuya_productI_is_bind',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}